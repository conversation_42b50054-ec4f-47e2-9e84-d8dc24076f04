-- DITIB Funeral App - Simplified RLS Policies (Infinite Recursion Fix)
-- Bu dosyayı Supabase SQL Editor'de çalıştırarak RLS politikalarını güncelleyin

-- 1. Tüm mevcut politikaları temizle
DROP POLICY IF EXISTS "Users can read their own data" ON public.users;
DROP POLICY IF EXISTS "Service role can manage users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can read users" ON public.users;
DROP POLICY IF EXISTS "Admin users can manage users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can read all users" ON public.users;
DROP POLICY IF EXISTS "Admin users can update users" ON public.users;
DROP POLICY IF EXISTS "Service role full access to users" ON public.users;
DROP POLICY IF EXISTS "Anonymous users can check emails" ON public.users;

DROP POLICY IF EXISTS "Users can read deceased" ON public.deceased;
DROP POLICY IF EXISTS "Service role can manage deceased" ON public.deceased;
DROP POLICY IF EXISTS "Authenticated users can read deceased" ON public.deceased;
DROP POLICY IF EXISTS "Service role full access to deceased" ON public.deceased;

DROP POLICY IF EXISTS "Users can read cases" ON public.cases;
DROP POLICY IF EXISTS "Service role can manage cases" ON public.cases;
DROP POLICY IF EXISTS "Authenticated users can read cases" ON public.cases;
DROP POLICY IF EXISTS "Admin users can manage cases" ON public.cases;
DROP POLICY IF EXISTS "Service role full access to cases" ON public.cases;

DROP POLICY IF EXISTS "Users can read tasks" ON public.tasks;
DROP POLICY IF EXISTS "Service role can manage tasks" ON public.tasks;
DROP POLICY IF EXISTS "Authenticated users can read tasks" ON public.tasks;
DROP POLICY IF EXISTS "Authenticated users can manage tasks" ON public.tasks;
DROP POLICY IF EXISTS "Admin users can read driver tasks" ON public.tasks;
DROP POLICY IF EXISTS "Driver users can manage own tasks" ON public.tasks;
DROP POLICY IF EXISTS "Admin users can manage driver tasks" ON public.tasks;
DROP POLICY IF EXISTS "Service role full access to tasks" ON public.tasks;

DROP POLICY IF EXISTS "Authenticated users can read task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Authenticated users can create task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Admin users can read driver task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Driver users can read own task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Admin and Driver users can create task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Service role full access to task_status_history" ON public.task_status_history;

DROP POLICY IF EXISTS "Users can read documents" ON public.documents;
DROP POLICY IF EXISTS "Service role can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Authenticated users can read documents" ON public.documents;
DROP POLICY IF EXISTS "Authenticated users can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Admin and Driver users can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Service role full access to documents" ON public.documents;

DROP POLICY IF EXISTS "Users can read notifications" ON public.notifications;
DROP POLICY IF EXISTS "Service role can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can read notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Admin users can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Service role full access to notifications" ON public.notifications;

DROP POLICY IF EXISTS "Authenticated users can read lookup_values" ON public.lookup_values;
DROP POLICY IF EXISTS "Authenticated users can manage lookup_values" ON public.lookup_values;
DROP POLICY IF EXISTS "Admin users can manage lookup_values" ON public.lookup_values;
DROP POLICY IF EXISTS "Service role full access to lookup_values" ON public.lookup_values;

-- 2. Basitleştirilmiş RLS Policies

-- USERS TABLE - Basit ve güvenli
CREATE POLICY "Authenticated users can read all users" ON public.users
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can update users" ON public.users
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to users" ON public.users
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Anonymous users can check emails" ON public.users
  FOR SELECT
  TO anon
  USING (true);

-- DECEASED TABLE
CREATE POLICY "Authenticated users can read deceased" ON public.deceased
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Service role full access to deceased" ON public.deceased
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- CASES TABLE
CREATE POLICY "Authenticated users can read cases" ON public.cases
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can manage cases" ON public.cases
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to cases" ON public.cases
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- TASKS TABLE
CREATE POLICY "Authenticated users can read tasks" ON public.tasks
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can manage tasks" ON public.tasks
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to tasks" ON public.tasks
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- TASK_STATUS_HISTORY TABLE
CREATE POLICY "Authenticated users can read task history" ON public.task_status_history
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create task history" ON public.task_status_history
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Service role full access to task_status_history" ON public.task_status_history
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- DOCUMENTS TABLE
CREATE POLICY "Authenticated users can read documents" ON public.documents
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can manage documents" ON public.documents
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to documents" ON public.documents
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- NOTIFICATIONS TABLE
CREATE POLICY "Authenticated users can read notifications" ON public.notifications
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can manage notifications" ON public.notifications
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to notifications" ON public.notifications
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- LOOKUP_VALUES TABLE
CREATE POLICY "Authenticated users can read lookup_values" ON public.lookup_values
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can manage lookup_values" ON public.lookup_values
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role full access to lookup_values" ON public.lookup_values
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 3. RLS'nin aktif olduğundan emin ol
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deceased ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lookup_values ENABLE ROW LEVEL SECURITY;

-- 4. Başarı mesajı
SELECT '✅ Simplified RLS Policies applied successfully!' as message;
SELECT 'Infinite recursion issues have been resolved.' as info;
SELECT 'All authenticated users now have appropriate access.' as note;
