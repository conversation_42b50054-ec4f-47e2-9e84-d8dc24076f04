# Tasks Tablosu Optimizasyon Analizi

## 1. Mevcut Durum Analizi

### Mock Data Yapısı (MyTasksScreen.tsx)
```typescript
{
  id: string,
  title: string,           // <PERSON>örev başlığı
  description: string,     // Görev açıklaması
  case_name: string,       // Vaka adı
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED',
  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW',
  due_time: string,        // Bitiş saati
  estimated_duration: string, // Ta<PERSON><PERSON> süre
  location: string,        // Konum
  created_at: string,      // Oluşturulma tarihi
  progress: number         // İlerleme yüzdesi
}
```

### Mevcut Supabase Tasks Tablosu
```sql
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  assignee_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
  task_type TEXT NOT NULL CHECK (task_type IN ('PICK_UP_FROM_MORGUE', 'TO_AIRPORT', 'TO_CONSULATE', 'DELIVERED')),
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACTIVE', 'DONE', 'FAILED', 'OVERDUE')),
  sub_status TEXT CHECK (sub_status IN ('PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'ON_HOLD')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT
);
```

## 2. Tespit Edilen Eksiklikler

### Mock Data'da Olan Ama Tabloda Olmayan Alanlar:
- ❌ `title` - Görev başlığı
- ❌ `description` - Görev açıklaması
- ❌ `priority` - Öncelik seviyesi
- ❌ `due_time` - Bitiş saati
- ❌ `estimated_duration` - Tahmini süre
- ❌ `location` - Konum bilgisi
- ❌ `progress` - İlerleme yüzdesi

### Status Uyumsuzlukları:
- **Mock Data:** `PENDING`, `IN_PROGRESS`, `COMPLETED`
- **Database:** `PENDING`, `ACTIVE`, `DONE`, `FAILED`, `OVERDUE`

## 3. Optimize Edilmiş Tablo Yapısı

```sql
-- Optimize edilmiş tasks tablosu
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  assignee_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
  
  -- Görev bilgileri
  task_type TEXT NOT NULL CHECK (task_type IN (
    'PICK_UP_FROM_MORGUE', 
    'TO_AIRPORT', 
    'TO_CONSULATE', 
    'DELIVERED', 
    'DOCUMENT_DELIVERY', 
    'FAMILY_MEETING', 
    'HOSPITAL_TRANSFER'
  )),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Durum ve öncelik
  status TEXT DEFAULT 'PENDING' CHECK (status IN (
    'PENDING', 
    'IN_PROGRESS', 
    'COMPLETED', 
    'FAILED', 
    'OVERDUE', 
    'CANCELLED'
  )),
  sub_status TEXT CHECK (sub_status IN (
    'PICKED_UP', 
    'IN_TRANSIT', 
    'DELIVERED', 
    'ON_HOLD', 
    'WAITING_APPROVAL'
  )),
  priority TEXT DEFAULT 'MEDIUM' CHECK (priority IN (
    'URGENT', 
    'HIGH', 
    'MEDIUM', 
    'LOW'
  )),
  
  -- Zaman bilgileri
  scheduled_at TIMESTAMP WITH TIME ZONE,
  due_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  estimated_duration_minutes INTEGER,
  
  -- Konum ve ilerleme
  location VARCHAR(500),
  progress_percentage INTEGER DEFAULT 0 CHECK (
    progress_percentage >= 0 AND progress_percentage <= 100
  ),
  
  -- Notlar ve metadata
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tasks_updated_at 
BEFORE UPDATE ON public.tasks
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 4. Migration Script

```sql
-- Mevcut tasks tablosuna yeni alanlar ekleme
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS title VARCHAR(255),
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'MEDIUM' 
  CHECK (priority IN ('URGENT', 'HIGH', 'MEDIUM', 'LOW')),
ADD COLUMN IF NOT EXISTS due_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS location VARCHAR(500),
ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 
  CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Status enum'unu güncelleme
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_status_check;
ALTER TABLE public.tasks ADD CONSTRAINT tasks_status_check 
CHECK (status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'OVERDUE', 'CANCELLED'));

-- Task type enum'unu genişletme
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_task_type_check;
ALTER TABLE public.tasks ADD CONSTRAINT tasks_task_type_check 
CHECK (task_type IN (
  'PICK_UP_FROM_MORGUE', 
  'TO_AIRPORT', 
  'TO_CONSULATE', 
  'DELIVERED', 
  'DOCUMENT_DELIVERY', 
  'FAMILY_MEETING', 
  'HOSPITAL_TRANSFER'
));

-- Mevcut verileri güncelleme
UPDATE public.tasks SET 
  title = CASE task_type
    WHEN 'PICK_UP_FROM_MORGUE' THEN 'Morgue\'dan Alma'
    WHEN 'TO_AIRPORT' THEN 'Havaalanına Nakil'
    WHEN 'TO_CONSULATE' THEN 'Konsolosluğa Nakil'
    WHEN 'DELIVERED' THEN 'Teslim'
    ELSE 'Görev'
  END,
  priority = 'MEDIUM',
  progress_percentage = CASE status
    WHEN 'DONE' THEN 100
    WHEN 'ACTIVE' THEN 50
    ELSE 0
  END
WHERE title IS NULL;

-- Status mapping
UPDATE public.tasks SET status = 'IN_PROGRESS' WHERE status = 'ACTIVE';
UPDATE public.tasks SET status = 'COMPLETED' WHERE status = 'DONE';
```

## 5. Güncellenmiş TypeScript Interface

```typescript
export interface TaskData {
  id: string;
  case_id: string;
  assignee_id?: string;
  
  // Görev bilgileri
  task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 
             'DELIVERED' | 'DOCUMENT_DELIVERY' | 'FAMILY_MEETING' | 
             'HOSPITAL_TRANSFER';
  title: string;
  description?: string;
  
  // Durum ve öncelik
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 
          'OVERDUE' | 'CANCELLED';
  sub_status?: 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 
               'ON_HOLD' | 'WAITING_APPROVAL';
  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
  
  // Zaman bilgileri
  scheduled_at?: string;
  due_at?: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration_minutes?: number;
  
  // Konum ve ilerleme
  location?: string;
  progress_percentage: number;
  
  // Notlar ve metadata
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // İlişkili veriler
  case?: {
    id: string;
    deceased_id: string;
    status: string;
    deceased?: {
      full_name: string;
      family_contact_name: string;
    };
  };
  assigned_to?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
}
```

## 6. TaskService Güncellemeleri

```typescript
// Güncellenmiş driver görevleri getirme fonksiyonu
async getDriverTasks(driverId: string, status?: string): Promise<TaskData[]> {
  try {
    let query = supabase
      .from('tasks')
      .select(`
        *,
        case:cases(
          id,
          status,
          deceased:deceased(
            full_name,
            family_contact_name
          )
        ),
        assigned_to:users(
          id,
          full_name,
          email,
          phone
        )
      `)
      .eq('assignee_id', driverId)
      .order('priority', { ascending: false })
      .order('due_at', { ascending: true });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching driver tasks:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('TaskService.getDriverTasks error:', error);
    return [];
  }
}

// İlerleme güncelleme fonksiyonu
async updateTaskProgress(taskId: string, progressPercentage: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('tasks')
      .update({ 
        progress_percentage: progressPercentage,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    if (error) {
      console.error('Error updating task progress:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('TaskService.updateTaskProgress error:', error);
    return false;
  }
}
```

## 7. Performans Optimizasyonları

```sql
-- Driver görevleri için index'ler
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_status 
ON public.tasks(assignee_id, status);

CREATE INDEX IF NOT EXISTS idx_tasks_priority_due_at 
ON public.tasks(priority DESC, due_at ASC);

CREATE INDEX IF NOT EXISTS idx_tasks_case_id 
ON public.tasks(case_id);

CREATE INDEX IF NOT EXISTS idx_tasks_scheduled_at 
ON public.tasks(scheduled_at);
```

## 8. Öneriler ve Sonraki Adımlar

### Acil Yapılması Gerekenler:
1. ✅ Migration script'ini çalıştır
2. ✅ TaskData interface'ini güncelle
3. ✅ TaskService'i yeni alanlarla güncelle
4. ✅ MyTasksScreen'i gerçek API'ye bağla

### Performans İyileştirmeleri:
1. ✅ Index'leri ekle
2. ✅ Supabase realtime subscription kullan
3. ✅ Pagination ekle (çok sayıda görev için)

### Güvenlik ve Validasyon:
1. ✅ RLS policies güncelle
2. ✅ Frontend validasyon ekle
3. ✅ Audit trail için task_status_history kullan

### Kullanıcı Deneyimi:
1. ✅ Loading states ekle
2. ✅ Error handling iyileştir
3. ✅ Offline support ekle
4. ✅ Push notifications entegre et

## 9. Lokasyon Tabanlı İlerleme Hesaplama Sistemi

### Görev Tamamlanma Yüzdesi Hesaplama Kuralları:
1. **Sürücü hedef noktaya yaklaştıkça**: 0% → 90% (lokasyon tabanlı)
2. **Sürücü hedefe ulaştığında**: Otomatik 90%
3. **"Görev Tamamlandı" butonuna basıldığında**: 100%

### TypeScript Interface Güncellemeleri:

```typescript
export interface TaskData {
  id: string;
  case_id: string;
  assignee_id?: string;
  
  // Görev bilgileri
  task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 
             'DELIVERED' | 'DOCUMENT_DELIVERY' | 'FAMILY_MEETING' | 
             'HOSPITAL_TRANSFER';
  title: string;
  description?: string;
  
  // Durum ve öncelik
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 
          'OVERDUE' | 'CANCELLED';
  sub_status?: 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 
               'ON_HOLD' | 'WAITING_APPROVAL';
  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
  
  // Zaman bilgileri
  scheduled_at?: string;
  due_at?: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration_minutes?: number;
  
  // Konum ve ilerleme
  location?: string;
  target_latitude?: number;
  target_longitude?: number;
  progress_percentage: number;
  
  // Notlar ve metadata
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // İlişkili veriler
  case?: {
    id: string;
    deceased_id: string;
    status: string;
    deceased?: {
      full_name: string;
      family_contact_name: string;
    };
  };
  assigned_to?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
}

// Lokasyon hesaplama için yardımcı interface
export interface LocationData {
  latitude: number;
  longitude: number;
}

export interface TaskProgressCalculation {
  taskId: string;
  currentLocation: LocationData;
  targetLocation: LocationData;
  calculatedProgress: number;
  distanceRemaining: number;
  isNearTarget: boolean;
}
```

### Lokasyon Tabanlı İlerleme Hesaplama Fonksiyonları:

```typescript
// Mesafe hesaplama (Haversine formülü)
function calculateDistance(
  lat1: number, lon1: number, 
  lat2: number, lon2: number
): number {
  const R = 6371; // Dünya'nın yarıçapı (km)
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// İlerleme yüzdesi hesaplama
function calculateTaskProgress(
  task: TaskData, 
  userLocation: LocationData
): TaskProgressCalculation {
  if (!task.target_latitude || !task.target_longitude) {
    return {
      taskId: task.id,
      currentLocation: userLocation,
      targetLocation: { latitude: 0, longitude: 0 },
      calculatedProgress: task.progress_percentage,
      distanceRemaining: 0,
      isNearTarget: false
    };
  }

  const targetLocation = {
    latitude: task.target_latitude,
    longitude: task.target_longitude
  };

  // Başlangıç konumu (görev başladığında kaydedilecek)
  const startLocation = getTaskStartLocation(task.id) || userLocation;
  
  const totalDistance = calculateDistance(
    startLocation.latitude, startLocation.longitude,
    targetLocation.latitude, targetLocation.longitude
  );
  
  const remainingDistance = calculateDistance(
    userLocation.latitude, userLocation.longitude,
    targetLocation.latitude, targetLocation.longitude
  );
  
  // İlerleme hesaplama (maksimum %90)
  const rawProgress = totalDistance > 0 
    ? ((totalDistance - remainingDistance) / totalDistance) * 90
    : 0;
  
  const calculatedProgress = Math.max(0, Math.min(90, Math.round(rawProgress)));
  
  // Hedefe yakınlık kontrolü (100m içinde)
  const isNearTarget = remainingDistance < 0.1; // 100 metre
  
  return {
    taskId: task.id,
    currentLocation: userLocation,
    targetLocation,
    calculatedProgress: isNearTarget ? 90 : calculatedProgress,
    distanceRemaining: remainingDistance,
    isNearTarget
  };
}

// Görev başlangıç konumunu kaydetme
function saveTaskStartLocation(taskId: string, location: LocationData): void {
  // AsyncStorage veya başka bir yöntemle kaydet
  const startLocations = JSON.parse(
    localStorage.getItem('taskStartLocations') || '{}'
  );
  startLocations[taskId] = location;
  localStorage.setItem('taskStartLocations', JSON.stringify(startLocations));
}

// Görev başlangıç konumunu getirme
function getTaskStartLocation(taskId: string): LocationData | null {
  const startLocations = JSON.parse(
    localStorage.getItem('taskStartLocations') || '{}'
  );
  return startLocations[taskId] || null;
}
```

### TaskService Güncellemeleri:

```typescript
// Lokasyon tabanlı ilerleme güncelleme
async updateTaskProgressWithLocation(
  taskId: string, 
  userLocation: LocationData
): Promise<TaskProgressCalculation | null> {
  try {
    const task = await this.getTaskById(taskId);
    if (!task) return null;

    const progressCalc = calculateTaskProgress(task, userLocation);
    
    // Sadece anlamlı değişiklik varsa güncelle (5% veya daha fazla)
    if (Math.abs(progressCalc.calculatedProgress - task.progress_percentage) >= 5) {
      await this.updateTaskProgress(taskId, progressCalc.calculatedProgress);
      
      // Hedefe yaklaşıldığında bildirim gönder
      if (progressCalc.isNearTarget && task.progress_percentage < 85) {
        await this.sendTaskNotification(taskId, 'NEAR_TARGET');
      }
    }
    
    return progressCalc;
  } catch (error) {
    console.error('TaskService.updateTaskProgressWithLocation error:', error);
    return null;
  }
}

// Görev tamamlama (manuel)
async completeTask(taskId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('tasks')
      .update({
        status: 'COMPLETED',
        progress_percentage: 100,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    if (error) {
      console.error('Error completing task:', error);
      return false;
    }

    // Başlangıç konumunu temizle
    const startLocations = JSON.parse(
      localStorage.getItem('taskStartLocations') || '{}'
    );
    delete startLocations[taskId];
    localStorage.setItem('taskStartLocations', JSON.stringify(startLocations));

    return true;
  } catch (error) {
    console.error('TaskService.completeTask error:', error);
    return false;
  }
}

// Görev başlatma
async startTask(taskId: string, startLocation: LocationData): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('tasks')
      .update({
        status: 'IN_PROGRESS',
        started_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    if (error) {
      console.error('Error starting task:', error);
      return false;
    }

    // Başlangıç konumunu kaydet
    saveTaskStartLocation(taskId, startLocation);
    
    return true;
  } catch (error) {
    console.error('TaskService.startTask error:', error);
    return false;
  }
}
```

## 10. Test Senaryoları

```sql
-- Test data ekleme (lokasyon bilgileri ile)
INSERT INTO public.tasks (
  case_id, assignee_id, task_type, title, description,
  status, priority, scheduled_at, due_at, 
  estimated_duration_minutes, location, 
  target_latitude, target_longitude, progress_percentage
) VALUES (
  (SELECT id FROM public.cases LIMIT 1),
  (SELECT id FROM public.users WHERE role = 'DRIVER' LIMIT 1),
  'PICK_UP_FROM_MORGUE',
  'Morgue\'dan Alma',
  'Hastane morgue\'undan cenaze alma',
  'PENDING',
  'HIGH',
  NOW() + INTERVAL '2 hours',
  NOW() + INTERVAL '4 hours',
  60,
  'Şişli Etfal Hastanesi Morgue',
  41.0602, -- Şişli Etfal Hastanesi koordinatları
  28.9942,
  0
);
```

Bu optimizasyon ile:
- ✅ Mock data kullanımı kaldırıldı
- ✅ Lokasyon tabanlı ilerleme hesaplama eklendi
- ✅ Görev %90'da durur, manuel tamamlama ile %100 olur
- ✅ Gerçek zamanlı konum takibi
- ✅ Akıllı bildirim sistemi