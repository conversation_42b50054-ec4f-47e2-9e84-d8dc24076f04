1. <PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>)

users                              # ADMIN · DRIVER · FAMILY
├─ id uuid PK
├─ role ENUM('ADMIN','DRIVER','FAMILY')
├─ full_name text · email UNIQUE · phone
├─ status ENUM('ACTIVE','ON_LEAVE','INACTIVE')
└─ created_at timestamptz DEFAULT now()

deceased                           # <PERSON><PERSON><PERSON> senkron (salt‑okunur)
├─ id uuid PK · ditib_member_id UNIQUE
├─ full_name · nationality · gender
├─ date_of_death · place_of_death · place_of_burial
├─ family_name · family_email · family_phone
└─ created_at

cases                              # cenaze vakası
├─ id uuid PK
├─ deceased_id     ↘ deceased.id  ON DELETE CASCADE
├─ family_user_id  uuid ↘ users.id (role=FAMILY)
├─ status          ENUM('OPEN','CLOSED','CANCELLED')
├─ burial_type     ENUM('DE','TR')
└─ created_at      timestamptz DEFAULT now()

tasks                              # süre<PERSON> adı<PERSON> (admin atar)
├─ id uuid PK
├─ case_id       ↘ cases.id ON DELETE CASCADE
├─ assignee_id   uuid NULL ↘ users.id   -- DRIVER (NULL = henüz atanmamış)
├─ task_type     ENUM('PICK_UP_FROM_MORGUE','TO_AIRPORT','TO_CONSULATE','DELIVERED')
├─ status        ENUM('PENDING','ACTIVE','DONE','FAILED','OVERDUE')
├─ sub_status    ENUM('PICKED_UP','IN_TRANSIT','DELIVERED','ON_HOLD') NULL
├─ scheduled_at  timestamptz
├─ started_at    timestamptz
├─ completed_at  timestamptz
└─ notes text NULL

task_status_history                # denetim izi
├─ id uuid PK
├─ task_id      ↘ tasks.id ON DELETE CASCADE
├─ actor_id     ↘ users.id (ADMIN/DRIVER)
├─ old_status / new_status · old_sub_status / new_sub_status
└─ changed_at   timestamptz DEFAULT now()

documents                          # evrak meta
├─ id uuid PK
├─ case_id     ↘ cases.id ON DELETE CASCADE
├─ doc_type    ENUM('DEATH_CERT','FORMUL_C','CARGO_WAYBILL','PHOTO')
├─ storage_path text
├─ uploaded_by ↘ users.id  -- ADMIN/DRIVER
└─ created_at  timestamptz DEFAULT now()

notifications                      # push / SMS / email logu
├─ id uuid PK · case_id ↘ cases.id
├─ channel ENUM('PUSH','SMS','EMAIL')
├─ to text · template text
├─ sent_at timestamptz
└─ status ENUM('SENT','FAIL')

lookup_values                      # kod sözlüğü (reserve future)
└─ id serial PK · category · code · label_tr · label_de

2. Görev Ataması – Admin Kontrollü

  1. Yeni vaka açıldığında Edge Function create_case PICK_UP_FROM_MORGUE görevini status=PENDING, assignee_id = NULL olarak ekler.

  2. ADMIN paneli görev kartında “Assign Driver” butonu → uygun DRIVER listesini (role='DRIVER' & status='ACTIVE') getirir.

  3. Seçim sonrası istemci PUT /tasks/:id/assign { assignee_id, scheduled_at? } çağrısı yapar.

  4. Edge Function assign_driver.ts:
UPDATE tasks
   SET assignee_id = :driver,
       status      = 'PENDING',
       scheduled_at= COALESCE(:scheduled_at, scheduled_at)
 WHERE id = :task_id
   AND status IN ('PENDING','FAILED');
INSERT INTO task_status_history (... old/new ...) VALUES (...);
``
  5. OneSignal push → seçilen DRIVER (“Size yeni görev atandı”);SMS / push → FAMILY (“Cenaze morgdan alınacak, sürücü planlandı”).
  6. DRIVER Başlat / Tamamla / alt‑durum günceller → yeni satır task_status_history. 
    - ADMIN gerektiğinde görevi başka sürücüye yeniden atayabilir (aynı endpoint).