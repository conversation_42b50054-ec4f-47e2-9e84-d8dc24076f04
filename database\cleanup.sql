-- DITIB Funeral App - Database Cleanup Script
-- ⚠️ UYARI: Bu script tüm verileri kalıcı olarak siler!
-- Sadece development/test ortamlarında kullanın!
-- Production'da kullanmadan önce mutlaka yedek alın!

-- Kullanım:
-- psql -h your-host -U postgres -d postgres -f database/cleanup.sql

-- ============================================================================
-- GÜVENLIK KONTROLÜ
-- ============================================================================

-- Bu script'in yanlışlıkla production'da çalışmasını önlemek için
-- A<PERSON>ağıdaki satırı uncomment edin ve database adını kontrol edin
-- SELECT current_database() as current_db;
-- \prompt 'Production database ise CTRL+C ile çıkın. Devam etmek için ENTER basın: ' confirm

-- ============================================================================
-- 1. TRIGGER'LARI KALDIR
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Trigger''lar kaldırılıyor...';
END $$;

-- Otomatik görev atama trigger'ları
DROP TRIGGER IF EXISTS trigger_assign_tasks ON public.tasks;
DROP TRIGGER IF EXISTS trigger_task_status_change ON public.tasks;
DROP TRIGGER IF EXISTS trigger_close_case ON public.tasks;

-- Updated_at trigger'ları
DROP TRIGGER IF EXISTS handle_updated_at_organisations ON public.organisations;
DROP TRIGGER IF EXISTS handle_updated_at_staff ON public.staff;
DROP TRIGGER IF EXISTS handle_updated_at_deceased ON public.deceased;
DROP TRIGGER IF EXISTS handle_updated_at_cases ON public.cases;
DROP TRIGGER IF EXISTS handle_updated_at_tasks ON public.tasks;
DROP TRIGGER IF EXISTS handle_updated_at_task_status_history ON public.task_status_history;
DROP TRIGGER IF EXISTS handle_updated_at_documents ON public.documents;
DROP TRIGGER IF EXISTS handle_updated_at_finance_transactions ON public.finance_transactions;
DROP TRIGGER IF EXISTS handle_updated_at_notifications ON public.notifications;
DROP TRIGGER IF EXISTS handle_updated_at_invitations ON public.invitations;

-- Eski trigger'lar (eğer varsa)
DROP TRIGGER IF EXISTS handle_task_changes ON public.tasks;
DROP TRIGGER IF EXISTS handle_updated_at_profiles ON public.profiles;
DROP TRIGGER IF EXISTS handle_updated_at_personnel ON public.personnel;

-- ============================================================================
-- 2. RLS POLİTİKALARINI KALDIR
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'RLS politikaları kaldırılıyor...';
END $$;

-- Organisations politikaları
DROP POLICY IF EXISTS "Everyone can view organisations" ON public.organisations;
DROP POLICY IF EXISTS "Admins can manage organisations" ON public.organisations;
DROP POLICY IF EXISTS "Staff can view their organisation" ON public.organisations;
DROP POLICY IF EXISTS "Authenticated users can view organisations" ON public.organisations;
DROP POLICY IF EXISTS "Authenticated users can manage organisations" ON public.organisations;

-- Staff politikaları
DROP POLICY IF EXISTS "Staff can view own record" ON public.staff;
DROP POLICY IF EXISTS "Staff can view organisation staff" ON public.staff;
DROP POLICY IF EXISTS "Admins can manage staff" ON public.staff;
DROP POLICY IF EXISTS "Staff can update own profile" ON public.staff;
DROP POLICY IF EXISTS "Authenticated users can view staff" ON public.staff;
DROP POLICY IF EXISTS "Authenticated users can manage staff" ON public.staff;

-- Deceased politikaları
DROP POLICY IF EXISTS "Staff can view deceased records" ON public.deceased;
DROP POLICY IF EXISTS "Admins can manage deceased records" ON public.deceased;
DROP POLICY IF EXISTS "Authenticated users can view deceased" ON public.deceased;
DROP POLICY IF EXISTS "Authenticated users can manage deceased" ON public.deceased;

-- Cases politikaları
DROP POLICY IF EXISTS "Staff can view organisation cases" ON public.cases;
DROP POLICY IF EXISTS "Staff can manage cases" ON public.cases;
DROP POLICY IF EXISTS "Authenticated users can view cases" ON public.cases;
DROP POLICY IF EXISTS "Authenticated users can manage cases" ON public.cases;

-- Tasks politikaları
DROP POLICY IF EXISTS "Staff can view relevant tasks" ON public.tasks;
DROP POLICY IF EXISTS "Staff can manage tasks" ON public.tasks;
DROP POLICY IF EXISTS "Authenticated users can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Authenticated users can manage tasks" ON public.tasks;

-- Task Status History politikaları
DROP POLICY IF EXISTS "Staff can view task history" ON public.task_status_history;
DROP POLICY IF EXISTS "Authenticated users can view task history" ON public.task_status_history;

-- Documents politikaları
DROP POLICY IF EXISTS "Staff can view documents" ON public.documents;
DROP POLICY IF EXISTS "Staff can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Authenticated users can view documents" ON public.documents;
DROP POLICY IF EXISTS "Authenticated users can manage documents" ON public.documents;

-- Finance Transactions politikaları
DROP POLICY IF EXISTS "Staff can view finance transactions" ON public.finance_transactions;
DROP POLICY IF EXISTS "Staff can manage finance transactions" ON public.finance_transactions;
DROP POLICY IF EXISTS "Authenticated users can view finance transactions" ON public.finance_transactions;
DROP POLICY IF EXISTS "Authenticated users can manage finance transactions" ON public.finance_transactions;

-- Notifications politikaları
DROP POLICY IF EXISTS "Staff can view notifications" ON public.notifications;
DROP POLICY IF EXISTS "Staff can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can view notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can manage notifications" ON public.notifications;

-- Invitations politikaları
DROP POLICY IF EXISTS "Staff can view invitations" ON public.invitations;
DROP POLICY IF EXISTS "Staff can manage invitations" ON public.invitations;
DROP POLICY IF EXISTS "Authenticated users can view invitations" ON public.invitations;
DROP POLICY IF EXISTS "Authenticated users can manage invitations" ON public.invitations;
DROP POLICY IF EXISTS "Authenticated users can create invitations" ON public.invitations;
DROP POLICY IF EXISTS "Authenticated users can update invitations" ON public.invitations;

-- Lookup Values politikaları
DROP POLICY IF EXISTS "Everyone can view lookup values" ON public.lookup_values;
DROP POLICY IF EXISTS "Staff can manage lookup values" ON public.lookup_values;
DROP POLICY IF EXISTS "Authenticated users can view lookup values" ON public.lookup_values;
DROP POLICY IF EXISTS "Authenticated users can manage lookup values" ON public.lookup_values;

-- Eski politikalar (eğer varsa)
DROP POLICY IF EXISTS "Users can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can manage profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can view personnel" ON public.personnel;
DROP POLICY IF EXISTS "Users can manage personnel" ON public.personnel;

-- ============================================================================
-- 3. FONKSİYONLARI KALDIR
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Fonksiyonlar kaldırılıyor...';
END $$;

-- Ana fonksiyonlar
DROP FUNCTION IF EXISTS public.handle_updated_at() CASCADE;
DROP FUNCTION IF EXISTS public.admin_of_org(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.assign_tasks() CASCADE;
DROP FUNCTION IF EXISTS public.create_case(UUID, TEXT, UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_task_status_change() CASCADE;
DROP FUNCTION IF EXISTS public.close_case() CASCADE;

-- Eski fonksiyonlar (eğer varsa)
DROP FUNCTION IF EXISTS public.assign_task_to_staff(UUID, UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.reassign_task(UUID, UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.update_task_status(UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.get_all_tasks_for_admin(UUID, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.send_notification(UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_invitation(UUID, TEXT, TEXT, TEXT, TEXT, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_invitation_by_token(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.accept_invitation(TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.close_case(UUID) CASCADE;

-- ============================================================================
-- 4. TABLOLARI KALDIR (Bağımlılık sırasına göre)
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Tablolar kaldırılıyor...';
END $$;

-- Bağımlı tablolar önce
DROP TABLE IF EXISTS public.notifications CASCADE;
DROP TABLE IF EXISTS public.documents CASCADE;
DROP TABLE IF EXISTS public.task_status_history CASCADE;
DROP TABLE IF EXISTS public.tasks CASCADE;
DROP TABLE IF EXISTS public.cases CASCADE;
DROP TABLE IF EXISTS public.deceased CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;
DROP TABLE IF EXISTS public.lookup_values CASCADE;

-- Eski tablolar (eğer varsa)
DROP TABLE IF EXISTS public.staff CASCADE;
DROP TABLE IF EXISTS public.organisations CASCADE;
DROP TABLE IF EXISTS public.finance_transactions CASCADE;
DROP TABLE IF EXISTS public.invitations CASCADE;

-- Eski tablolar (eğer varsa)
DROP TABLE IF EXISTS public.profiles CASCADE;
DROP TABLE IF EXISTS public.personnel CASCADE;
DROP TABLE IF EXISTS public.audit_logs CASCADE;
DROP TABLE IF EXISTS public.case_timeline CASCADE;

-- ============================================================================
-- 5. EXTENSION'LARI KONTROL ET (Kaldırma)
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Extension''lar kontrol ediliyor...';

    -- uuid-ossp extension'ını kaldır (eğer başka yerde kullanılmıyorsa)
    -- DROP EXTENSION IF EXISTS "uuid-ossp";

    -- pgcrypto extension'ını kaldır (eğer başka yerde kullanılmıyorsa)
    -- DROP EXTENSION IF EXISTS "pgcrypto";

    RAISE NOTICE 'Extension''lar korundu (başka sistemler tarafından kullanılıyor olabilir)';
END $$;

-- ============================================================================
-- 6. SEQUENCE'LARI KALDIR
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Sequence''lar kaldırılıyor...';
END $$;

DROP SEQUENCE IF EXISTS public.lookup_values_id_seq CASCADE;

-- ============================================================================
-- 7. DOĞRULAMA
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Temizlik doğrulaması yapılıyor...';
END $$;

-- Kalan tabloları kontrol et
SELECT
    'Kalan tablolar:' as info,
    string_agg(tablename, ', ') as tables
FROM pg_tables
WHERE schemaname = 'public'
  AND tablename NOT IN ('spatial_ref_sys'); -- PostGIS tablosu hariç

-- Kalan fonksiyonları kontrol et
SELECT
    'Kalan fonksiyonlar:' as info,
    string_agg(routine_name, ', ') as functions
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_type = 'FUNCTION'
  AND routine_name NOT LIKE 'st_%'; -- PostGIS fonksiyonları hariç

-- Kalan trigger'ları kontrol et
SELECT
    'Kalan trigger''lar:' as info,
    string_agg(trigger_name, ', ') as triggers
FROM information_schema.triggers
WHERE trigger_schema = 'public';

-- ============================================================================
-- 8. BAŞARI MESAJI
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '============================================================================';
    RAISE NOTICE '✅ DITIB Funeral App veritabanı başarıyla temizlendi!';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Temizlenen öğeler:';
    RAISE NOTICE '   • 11 tablo (organisations, staff, deceased, cases, tasks, vb.)';
    RAISE NOTICE '   • 10+ trigger (otomatik görev atama, updated_at, vb.)';
    RAISE NOTICE '   • 30+ RLS politikası (organizasyon bazlı güvenlik)';
    RAISE NOTICE '   • 10+ fonksiyon (iş mantığı ve yardımcı fonksiyonlar)';
    RAISE NOTICE '   • 1 sequence (lookup_values_id_seq)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Yeni kurulum için:';
    RAISE NOTICE '   1. schema.sql';
    RAISE NOTICE '   2. functions.sql';
    RAISE NOTICE '   3. security-policies.sql';
    RAISE NOTICE '   4. test-data.sql (opsiyonel)';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  Extension''lar korundu (uuid-ossp, pgcrypto)';
    RAISE NOTICE '============================================================================';
END $$;

-- Son kontrol mesajı
SELECT '🎯 Cleanup tamamlandı! Yeni kurulum için hazır.' as result;
