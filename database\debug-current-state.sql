-- DITIB Funeral App - Current Database State Debug
-- Bu dosyayı Supabase SQL Editor'de çalıştırarak mevcut durumu kontrol edin

-- 1. Mevcut tabloları listele
SELECT 
  'Available Tables:' as info,
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- 2. Users tablosu var mı kontrol et
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
    RAISE NOTICE '✅ Users tablosu mevcut';
    
    -- Users sayısını kontrol et
    RAISE NOTICE 'Users tablosunda % kayıt var', (SELECT COUNT(*) FROM public.users);
    
    -- Tüm kullanıcıları listele
    RAISE NOTICE 'Mevcut kullanıcılar:';
    FOR rec IN SELECT email, full_name, role FROM public.users ORDER BY email LOOP
      RAISE NOTICE '  - %: % (%)', rec.email, rec.full_name, rec.role;
    END LOOP;
  ELSE
    RAISE NOTICE '❌ Users tablosu bulunamadı!';
  END IF;
END $$;

-- 3. Staff tablosu var mı kontrol et (eski sistem)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'staff') THEN
    RAISE NOTICE '⚠️ Staff tablosu hala mevcut (eski sistem)';
    
    -- Staff sayısını kontrol et
    RAISE NOTICE 'Staff tablosunda % kayıt var', (SELECT COUNT(*) FROM public.staff);
    
    -- Staff kullanıcılarını listele
    RAISE NOTICE 'Staff kullanıcıları:';
    FOR rec IN SELECT email, full_name, role FROM public.staff ORDER BY email LIMIT 10 LOOP
      RAISE NOTICE '  - %: % (%)', rec.email, rec.full_name, rec.role;
    END LOOP;
  ELSE
    RAISE NOTICE '✅ Staff tablosu yok (yeni sistem)';
  END IF;
END $$;

-- 4. <EMAIL> kullanıcısını özel olarak ara
DO $$
DECLARE
  user_found BOOLEAN := FALSE;
BEGIN
  -- Users tablosunda ara
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
    IF EXISTS (SELECT 1 FROM public.users WHERE email = '<EMAIL>') THEN
      RAISE NOTICE '✅ <EMAIL> USERS tablosunda bulundu';
      FOR rec IN SELECT email, full_name, role, status FROM public.users WHERE email = '<EMAIL>' LOOP
        RAISE NOTICE '  - Email: %, Name: %, Role: %, Status: %', rec.email, rec.full_name, rec.role, rec.status;
      END LOOP;
      user_found := TRUE;
    END IF;
  END IF;
  
  -- Staff tablosunda ara (eski sistem)
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'staff') THEN
    IF EXISTS (SELECT 1 FROM public.staff WHERE email = '<EMAIL>') THEN
      RAISE NOTICE '⚠️ <EMAIL> STAFF tablosunda bulundu (eski sistem)';
      FOR rec IN SELECT email, full_name, role FROM public.staff WHERE email = '<EMAIL>' LOOP
        RAISE NOTICE '  - Email: %, Name: %, Role: %', rec.email, rec.full_name, rec.role;
      END LOOP;
      user_found := TRUE;
    END IF;
  END IF;
  
  IF NOT user_found THEN
    RAISE NOTICE '❌ <EMAIL> hiçbir tabloda bulunamadı!';
  END IF;
END $$;

-- 5. Auth.users'da <EMAIL> var mı kontrol et
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    RAISE NOTICE '✅ <EMAIL> AUTH.USERS tablosunda bulundu';
    FOR rec IN SELECT email, created_at, email_confirmed_at FROM auth.users WHERE email = '<EMAIL>' LOOP
      RAISE NOTICE '  - Email: %, Created: %, Confirmed: %', rec.email, rec.created_at, rec.email_confirmed_at;
    END LOOP;
  ELSE
    RAISE NOTICE '❌ <EMAIL> AUTH.USERS tablosunda bulunamadı';
  END IF;
END $$;

-- 6. Özet rapor
SELECT '=== DATABASE STATE SUMMARY ===' as summary;

-- Tablo durumu
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') 
    THEN '✅ Users Table EXISTS' 
    ELSE '❌ Users Table MISSING' 
  END as users_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'staff') 
    THEN '⚠️ Staff Table EXISTS (old system)' 
    ELSE '✅ Staff Table REMOVED (new system)' 
  END as staff_table_status;

-- Kullanıcı durumu
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') 
         AND EXISTS (SELECT 1 FROM public.users WHERE email = '<EMAIL>')
    THEN '✅ <EMAIL> in USERS table' 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'staff') 
         AND EXISTS (SELECT 1 FROM public.staff WHERE email = '<EMAIL>')
    THEN '⚠️ <EMAIL> in STAFF table (old system)'
    ELSE '❌ <EMAIL> NOT FOUND in any table' 
  END as user_location_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
    THEN '✅ <EMAIL> has AUTH account' 
    ELSE '❌ <EMAIL> NO AUTH account' 
  END as auth_status;

-- 7. Sonuç
SELECT '🔍 Debug check completed! Check the output above for current database state.' as result;
