# DITIB Funeral App - Database Migration Summary

## 🎯 Migration Overview
**Version:** 4.0 - Fully aligned with DATABASE-SYSTEM.md  
**Date:** 2024-01-XX  
**Status:** ✅ Complete

## 📋 Major Changes

### 1. Table Structure Changes

#### ❌ Removed Tables
- `staff` → Replaced with `users`
- `organisations` → Removed (not in DATABASE-SYSTEM.md)
- `finance_transactions` → Removed (not needed)
- `invitations` → Removed (not in DATABASE-SYSTEM.md)

#### ✅ New/Updated Tables

**`users` (NEW)**
- Replaces `staff` table
- Columns: `id`, `role`, `full_name`, `email`, `phone`, `status`, `created_at`
- Roles: `ADMIN`, `DRIVER`, `FAMILY`
- Status: `ACTIVE`, `ON_LEAVE`, `INACTIVE`

**`deceased` (UPDATED)**
- Removed: `organisation_id`, `date_of_birth`, `birth_place`, `father_name`, `mother_name`, `id_number`, `updated_at`
- Added: `place_of_burial`, `family_name`, `family_email`, `family_phone`
- Simplified structure according to DATABASE-SYSTEM.md

**`cases` (UPDATED)**
- Removed: `organisation_id`, `created_by`, `family_contact_name`, `family_phone`, `family_email`, `updated_at`
- Added: `family_user_id` (references users table)
- Simplified structure

**`tasks` (UPDATED)**
- Task types changed from `WASH`, `PRAYER`, `LOCAL_TRANSFER`, `AIR_TRANSFER`, `BURIAL`
- To: `PICK_UP_FROM_MORGUE`, `TO_AIRPORT`, `TO_CONSULATE`, `DELIVERED`
- Status changed from `PENDING`, `STARTED`, `DONE`, `FAILED`, `OVERDUE`
- To: `PENDING`, `ACTIVE`, `DONE`, `FAILED`, `OVERDUE`
- Removed: `updated_at`

**`task_status_history` (UPDATED)**
- Changed `actor` to `actor_id` (references users table)

**`documents` (UPDATED)**
- Document types changed from `DEATH_CERT`, `LEICHENPASS`, `FORMUL_C`, `FLIGHT_WAYBILL`, `PHOTO`, `ID_DOCUMENT`, `MEDICAL_REPORT`, `OTHER`
- To: `DEATH_CERT`, `FORMUL_C`, `CARGO_WAYBILL`, `PHOTO`
- Removed: `verified_by`, `verified_at`, `updated_at`

**`notifications` (UPDATED)**
- Channel types changed from `SMS`, `EMAIL`
- To: `PUSH`, `SMS`, `EMAIL`

### 2. Function Changes

#### ❌ Removed Functions
- `admin_of_org()` → Replaced with `get_admin_user()`
- `handle_updated_at()` → No longer needed (no updated_at columns)

#### ✅ New/Updated Functions

**`get_admin_user()` (NEW)**
- Finds first ADMIN user with ACTIVE status
- Replaces organisation-based admin lookup

**`assign_tasks()` (UPDATED)**
- Simplified: All tasks assigned to admin users
- No more complex role-based assignment
- Follows DATABASE-SYSTEM.md: "admin role staff to be assigned all tasks"

**`create_case()` (UPDATED)**
- Parameters changed from `(deceased_id, burial_type, organisation_id, family_contact_name, family_phone, family_email)`
- To: `(deceased_id, family_user_id, burial_type)`
- Creates `PICK_UP_FROM_MORGUE` task instead of `WASH` and `PRAYER`

**`handle_task_status_change()` (UPDATED)**
- Updated for new task types and workflow
- Handles `PICK_UP_FROM_MORGUE` → `TO_AIRPORT` → `TO_CONSULATE` → `DELIVERED` flow
- Uses deceased table for family contact info

**`close_case()` (UPDATED)**
- Triggers on `DELIVERED` task completion instead of `BURIAL`

### 3. Application Code Changes

#### TypeScript Types (`app/lib/supabase.ts`)
- Updated all database types to match new schema
- Removed `staff`, `organisations`, `finance_transactions`, `invitations` types
- Updated `users`, `deceased`, `cases`, `tasks`, `documents`, `notifications` types

#### Services (`app/services/`)
- **`adminService.ts`**: Updated to use `users` table instead of `staff`
- **`documentService.ts`**: Updated document types and removed verification fields

#### Screens (`app/screens/`)
- **`CaseListScreen.tsx`**: Updated to use new case structure with family info from deceased table

### 4. Test Data Changes

#### Updated Test Data (`database/test-data.sql`)
- Created users with ADMIN, DRIVER, FAMILY roles
- Updated deceased records with family contact information
- Created cases with family_user_id references
- Updated tasks with new task types and workflow
- Updated documents with new document types
- Updated notifications with new channel types

## 🚀 Migration Steps

### 1. Backup Current Database
```sql
-- Run before migration
pg_dump your_database > backup_before_migration.sql
```

### 2. Run Migration Scripts (In Order)
```bash
# 1. Clean existing data
psql -f database/cleanup.sql

# 2. Create new schema
psql -f database/schema.sql

# 3. Create functions and triggers
psql -f database/functions.sql

# 4. Insert test data
psql -f database/test-data.sql
```

### 3. Update Application
- All TypeScript types updated
- All service methods updated
- All screen components updated

## ✅ Verification Checklist

- [ ] All tables created successfully
- [ ] All functions and triggers working
- [ ] Test data inserted correctly
- [ ] Application compiles without errors
- [ ] All screens load properly
- [ ] Task assignment works
- [ ] Case workflow functions
- [ ] Document upload works
- [ ] Notifications are sent

## 📊 Data Mapping

### Users Migration
```sql
-- Old staff table → New users table
staff.full_name → users.full_name
staff.email → users.email
staff.phone → users.phone
staff.role (GASSAL|IMAM|DRIVER|LOGISTICS|ADMIN) → users.role (ADMIN|DRIVER|FAMILY)
staff.is_active → users.status (ACTIVE|INACTIVE)
```

### Cases Migration
```sql
-- Old cases table → New cases table
cases.deceased_id → cases.deceased_id
cases.burial_type → cases.burial_type
cases.status → cases.status
cases.family_contact_name → deceased.family_name
cases.family_email → deceased.family_email
cases.family_phone → deceased.family_phone
```

## 🔧 Post-Migration Tasks

1. **Update Environment Variables**
   - Verify Supabase connection strings
   - Update any hardcoded table references

2. **Test Application Features**
   - Login functionality
   - Case creation and management
   - Task assignment and updates
   - Document upload and management
   - Notifications

3. **Performance Optimization**
   - Verify all indexes are created
   - Check query performance
   - Monitor database connections

## 📞 Support

If you encounter any issues during migration:
1. Check the error logs
2. Verify all scripts ran successfully
3. Ensure test data is properly inserted
4. Contact development team if needed

---

**Migration completed successfully! 🎉**  
Database is now fully aligned with DATABASE-SYSTEM.md specifications.
