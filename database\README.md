# DITIB Funeral App - Database

## 📁 Database Files Overview

This directory contains all the essential database files for the DITIB Funeral App, fully aligned with `DATABASE-SYSTEM.md` specifications.

### 📋 File Descriptions

| File | Purpose | Usage Order |
|------|---------|-------------|
| `DATABASE-SYSTEM.md` | 📖 **System Specification** | Reference document |
| `cleanup.sql` | 🧹 **Database Cleanup** | 1️⃣ Run first |
| `schema.sql` | 🏗️ **Database Schema** | 2️⃣ Run second |
| `functions.sql` | ⚙️ **Functions & Triggers** | 3️⃣ Run third |
| `test-data.sql` | 🧪 **Test Data** | 4️⃣ Run last |
| `MIGRATION-SUMMARY.md` | 📊 **Migration Guide** | Reference document |
| `README.md` | 📚 **This file** | Documentation |

## 🚀 Quick Setup

### 1. Fresh Database Setup
```bash
# Clean setup (removes all existing data)
psql -d your_database -f cleanup.sql
psql -d your_database -f schema.sql
psql -d your_database -f functions.sql
psql -d your_database -f test-data.sql
```

### 2. Schema Only (No Test Data)
```bash
# Production setup
psql -d your_database -f cleanup.sql
psql -d your_database -f schema.sql
psql -d your_database -f functions.sql
```

## 📊 Database Structure

### Core Tables
- **`users`** - Admin, Driver, Family users
- **`deceased`** - Deceased person information (DITIB sync)
- **`cases`** - Funeral cases
- **`tasks`** - Process steps (PICK_UP_FROM_MORGUE → TO_AIRPORT → TO_CONSULATE → DELIVERED)
- **`task_status_history`** - Audit trail
- **`documents`** - Document metadata
- **`notifications`** - Push/SMS/Email logs
- **`lookup_values`** - Code dictionary

### Key Features
- ✅ **Auto UUID generation** for all primary keys
- ✅ **Automatic task assignment** to admin users
- ✅ **Workflow automation** with triggers
- ✅ **Audit trail** for all task changes
- ✅ **Notification system** for family updates
- ✅ **Row Level Security** enabled

## 🔧 Functions & Triggers

### Functions
- `get_admin_user()` - Find active admin user
- `assign_tasks()` - Auto-assign tasks to admin
- `create_case()` - Create case with initial task
- `handle_task_status_change()` - Workflow automation
- `close_case()` - Auto-close completed cases

### Triggers
- `trigger_assign_tasks` - Auto-assign new tasks
- `trigger_task_status_change` - Handle status updates
- `trigger_close_case` - Auto-close cases

## 🧪 Test Data

The test data includes:
- **3 Admin users** (Köln, Düsseldorf, Berlin)
- **3 Driver users** (Köln, Düsseldorf, Berlin)
- **3 Family users** (Sample families)
- **3 Deceased records** with family contact info
- **3 Cases** in different stages
- **Multiple Tasks** showing workflow
- **Sample Documents** and **Notifications**

### Test Login Credentials
```
👥 Admin: <EMAIL>, <EMAIL>, <EMAIL>
🚗 Driver: <EMAIL>, <EMAIL>, <EMAIL>
👨‍👩‍👧‍👦 Family: <EMAIL>, <EMAIL>, <EMAIL>
```

## 📋 Verification Queries

After setup, verify with these queries:

```sql
-- Check table counts
SELECT 'Users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Deceased', COUNT(*) FROM deceased
UNION ALL
SELECT 'Cases', COUNT(*) FROM cases
UNION ALL
SELECT 'Tasks', COUNT(*) FROM tasks
UNION ALL
SELECT 'Documents', COUNT(*) FROM documents
UNION ALL
SELECT 'Notifications', COUNT(*) FROM notifications;

-- Check workflow
SELECT 
  c.id as case_id,
  d.full_name as deceased_name,
  t.task_type,
  t.status,
  t.sub_status
FROM cases c
JOIN deceased d ON c.deceased_id = d.id
JOIN tasks t ON t.case_id = c.id
ORDER BY c.created_at, t.task_type;
```

## ⚠️ Important Notes

1. **Backup First**: Always backup before running cleanup.sql
2. **Order Matters**: Run scripts in the specified order
3. **Test Environment**: Test all scripts in development first
4. **RLS Enabled**: Row Level Security is enabled on all tables
5. **UUID Primary Keys**: All tables use UUID primary keys

## 🔄 Updates

When updating the database:
1. Update `DATABASE-SYSTEM.md` first
2. Modify schema.sql if needed
3. Update functions.sql for new logic
4. Update test-data.sql for new test cases
5. Document changes in MIGRATION-SUMMARY.md

## 📞 Support

For database issues:
1. Check PostgreSQL logs
2. Verify all scripts ran without errors
3. Check RLS policies if access issues
4. Ensure proper user permissions

---

**Database Version:** 4.0 - Fully aligned with DATABASE-SYSTEM.md  
**Last Updated:** 2024-01-XX  
**Status:** ✅ Production Ready
