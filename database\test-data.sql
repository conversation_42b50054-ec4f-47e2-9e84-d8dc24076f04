-- DITIB Funeral App - Test Data
-- Based on DATABASE-SYSTEM.md specifications
-- Version: 4.0 - Fully aligned with DATABASE-SYSTEM.md

-- Clear existing data (in dependency order)
DELETE FROM public.notifications;
DELETE FROM public.documents;
DELETE FROM public.task_status_history;
DELETE FROM public.tasks;
DELETE FROM public.cases;
DELETE FROM public.deceased;
DELETE FROM public.users;
DELETE FROM public.lookup_values;

-- 1. LOOKUP VALUES (kod sözlüğü - reserve future)
INSERT INTO public.lookup_values (category, code, label_tr, label_de) VALUES
-- Task Types (DATABASE-SYSTEM.md'ye göre)
('task_type', 'PICK_UP_FROM_MORGUE', 'Morgdan Alma', 'Abholung vom Leichenschauhaus'),
('task_type', 'TO_AIRPORT', '<PERSON><PERSON><PERSON><PERSON>na <PERSON>', 'Transport zum Flughafen'),
('task_type', 'TO_CONSULATE', 'Konsolosluğa Nakil', 'Transport zum Konsulat'),
('task_type', 'DELIVERED', 'Teslim Edildi', 'Geliefert'),

-- Task Status (DATABASE-SYSTEM.md'ye göre)
('task_status', 'PENDING', 'Bekliyor', 'Wartend'),
('task_status', 'ACTIVE', 'Aktif', 'Aktiv'),
('task_status', 'DONE', 'Tamamlandı', 'Abgeschlossen'),
('task_status', 'FAILED', 'Başarısız', 'Fehlgeschlagen'),
('task_status', 'OVERDUE', 'Gecikmiş', 'Überfällig'),

-- Sub Status (DATABASE-SYSTEM.md'ye göre)
('sub_status', 'PICKED_UP', 'Teslim Alındı', 'Abgeholt'),
('sub_status', 'IN_TRANSIT', 'Yolda', 'Unterwegs'),
('sub_status', 'DELIVERED', 'Teslim Edildi', 'Geliefert'),
('sub_status', 'ON_HOLD', 'Beklemede', 'Wartend'),

-- User Roles (DATABASE-SYSTEM.md'ye göre)
('role', 'ADMIN', 'Yönetici', 'Administrator'),
('role', 'DRIVER', 'Şoför', 'Fahrer'),
('role', 'FAMILY', 'Aile', 'Familie'),

-- Document Types (DATABASE-SYSTEM.md'ye göre)
('doc_type', 'DEATH_CERT', 'Ölüm Belgesi', 'Sterbeurkunde'),
('doc_type', 'FORMUL_C', 'Formül C', 'Formular C'),
('doc_type', 'CARGO_WAYBILL', 'Kargo Belgesi', 'Frachtbrief'),
('doc_type', 'PHOTO', 'Fotoğraf', 'Foto');

-- 2. USERS (ADMIN · DRIVER · FAMILY)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.users (role, full_name, email, phone, status) VALUES
-- Admin users
('ADMIN', 'Ahmet Yılmaz', '<EMAIL>', '+49 ************', 'ACTIVE'),
('ADMIN', 'Fatma Şahin', '<EMAIL>', '+49 ************', 'ACTIVE'),
('ADMIN', 'Zeynep Arslan', '<EMAIL>', '+49 30 111 2222', 'ACTIVE'),

-- Driver users
('DRIVER', 'Hasan Kaya', '<EMAIL>', '+49 ************', 'ACTIVE'),
('DRIVER', 'Mustafa Çelik', '<EMAIL>', '+49 ************', 'ACTIVE'),
('DRIVER', 'Osman Güler', '<EMAIL>', '+49 30 333 4444', 'ACTIVE'),

-- Family users
('FAMILY', 'Mehmet Yıldız', '<EMAIL>', '+49 ************', 'ACTIVE'),
('FAMILY', 'Ayşe Yılmaz', '<EMAIL>', '+49 ************', 'ACTIVE'),
('FAMILY', 'Ali Özkan', '<EMAIL>', '+49 30 555 7777', 'ACTIVE'),

-- Test user for debugging
('ADMIN', 'Test Kullanıcısı', '<EMAIL>', '+49 ************', 'ACTIVE');

-- 3. DECEASED (DITIB senkron - salt-okunur)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.deceased (ditib_member_id, full_name, nationality, gender, date_of_death, place_of_death, place_of_burial, family_name, family_email, family_phone) VALUES
('DITIB123456', 'Ayşe Yıldız', 'TR', 'FEMALE', '2024-01-15 10:30:00+01', 'Köln Üniversite Hastanesi', 'Köln Merkez Mezarlığı', 'Mehmet Yıldız', '<EMAIL>', '+49 ************'),
('DITIB789012', 'Mehmet Yılmaz', 'TR', 'MALE', '2024-01-16 14:15:00+01', 'Düsseldorf Merkez Hastanesi', 'Türkiye - Ankara', 'Ayşe Yılmaz', '<EMAIL>', '+49 ************'),
('DITIB345678', 'Fatma Özkan', 'TR', 'FEMALE', '2024-01-17 08:45:00+01', 'Berlin Charité', 'Berlin Türk Mezarlığı', 'Ali Özkan', '<EMAIL>', '+49 30 555 7777');

-- 4. CASES (cenaze vakası)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.cases (deceased_id, family_user_id, status, burial_type) VALUES
-- Ayşe Yıldız vakası (Almanya'da defin)
((SELECT id FROM public.deceased WHERE full_name = 'Ayşe Yıldız'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'),
 'OPEN', 'DE'),

-- Mehmet Yılmaz vakası (Türkiye'ye nakil)
((SELECT id FROM public.deceased WHERE full_name = 'Mehmet Yılmaz'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'),
 'OPEN', 'TR'),

-- Fatma Özkan vakası (Almanya'da defin - tamamlanmış)
((SELECT id FROM public.deceased WHERE full_name = 'Fatma Özkan'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'),
 'CLOSED', 'DE');

-- 5. TASKS (süreç adımları - admin atar)
-- Based on DATABASE-SYSTEM.md specification
-- NOT: Trigger'ları tetiklememek için manuel INSERT yapıyoruz
INSERT INTO public.tasks (case_id, assignee_id, task_type, status, sub_status, scheduled_at, started_at, completed_at, notes) VALUES
-- Ayşe Yıldız vakası görevleri (henüz başlamadı)
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Ayşe Yıldız'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'PICK_UP_FROM_MORGUE', 'PENDING', NULL, NOW() + INTERVAL '2 hours', NULL, NULL, 'Morgdan alınacak'),

-- Mehmet Yılmaz vakası görevleri (devam ediyor)
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'PICK_UP_FROM_MORGUE', 'DONE', 'DELIVERED', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours', 'Morgdan alındı'),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'TO_AIRPORT', 'ACTIVE', 'IN_TRANSIT', NOW(), NOW() - INTERVAL '2 hours', NULL, 'Havaalanına gidiyor'),

-- Fatma Özkan vakası görevleri (tamamlanmış)
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Fatma Özkan'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'PICK_UP_FROM_MORGUE', 'DONE', 'DELIVERED', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days', NOW() - INTERVAL '2 days 23 hours', 'Morgdan alındı'),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Fatma Özkan'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'TO_AIRPORT', 'DONE', 'DELIVERED', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day 23 hours', 'Havaalanına teslim edildi'),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Fatma Özkan'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'TO_CONSULATE', 'DONE', 'DELIVERED', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours', 'Konsolosluğa teslim edildi'),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Fatma Özkan'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'DELIVERED', 'DONE', 'DELIVERED', NOW() - INTERVAL '12 hours', NOW() - INTERVAL '12 hours', NOW() - INTERVAL '6 hours', 'Son teslim tamamlandı');

-- 6. TASK_STATUS_HISTORY (denetim izi)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.task_status_history (task_id, actor_id, old_status, new_status, old_sub_status, new_sub_status) VALUES
-- Mehmet Yılmaz PICK_UP_FROM_MORGUE görevi geçmişi
((SELECT t.id FROM public.tasks t JOIN public.cases c ON t.case_id = c.id JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz' AND t.task_type = 'PICK_UP_FROM_MORGUE'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'PENDING', 'ACTIVE', NULL, NULL),

((SELECT t.id FROM public.tasks t JOIN public.cases c ON t.case_id = c.id JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz' AND t.task_type = 'PICK_UP_FROM_MORGUE'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'ACTIVE', 'DONE', NULL, 'PICKED_UP'),

-- Mehmet Yılmaz TO_AIRPORT görevi geçmişi
((SELECT t.id FROM public.tasks t JOIN public.cases c ON t.case_id = c.id JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz' AND t.task_type = 'TO_AIRPORT'),
 (SELECT id FROM public.users WHERE email = '<EMAIL>'), 'PENDING', 'ACTIVE', NULL, 'IN_TRANSIT');

-- 7. DOCUMENTS (evrak meta)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.documents (case_id, doc_type, storage_path, uploaded_by) VALUES
-- Ayşe Yıldız vakası belgeleri
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Ayşe Yıldız'),
 'DEATH_CERT', 'documents/ayse_yildiz/death_cert.pdf',
 (SELECT id FROM public.users WHERE email = '<EMAIL>')),

-- Mehmet Yılmaz vakası belgeleri
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 'FORMUL_C', 'documents/mehmet_yilmaz/formul_c.pdf',
 (SELECT id FROM public.users WHERE email = '<EMAIL>')),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 'CARGO_WAYBILL', 'documents/mehmet_yilmaz/cargo_waybill.pdf',
 (SELECT id FROM public.users WHERE email = '<EMAIL>'));


-- 8. NOTIFICATIONS (push / SMS / email logu)
-- Based on DATABASE-SYSTEM.md specification
INSERT INTO public.notifications (case_id, channel, to_address, template, sent_at, status) VALUES
-- Mehmet Yılmaz vakası bildirimleri
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 'SMS', '+49 ************', 'TASK_PICKED_UP', NOW() - INTERVAL '1 day', 'SENT'),

((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Mehmet Yılmaz'),
 'EMAIL', '<EMAIL>', 'TASK_IN_TRANSIT', NOW() - INTERVAL '2 hours', 'SENT'),

-- Fatma Özkan vakası bildirimleri
((SELECT c.id FROM public.cases c JOIN public.deceased d ON c.deceased_id = d.id WHERE d.full_name = 'Fatma Özkan'),
 'PUSH', '<EMAIL>', 'CASE_COMPLETED', NOW() - INTERVAL '1 day', 'SENT');

-- KONTROL SORGULARI
SELECT 'Users:' as info, COUNT(*) as count FROM public.users;
SELECT 'Deceased:' as info, COUNT(*) as count FROM public.deceased;
SELECT 'Cases:' as info, COUNT(*) as count FROM public.cases;
SELECT 'Tasks:' as info, COUNT(*) as count FROM public.tasks;
SELECT 'Documents:' as info, COUNT(*) as count FROM public.documents;
SELECT 'Notifications:' as info, COUNT(*) as count FROM public.notifications;
SELECT 'Lookup Values:' as info, COUNT(*) as count FROM public.lookup_values;

-- Başarı mesajı
SELECT '✅ Test data created successfully according to DATABASE-SYSTEM.md!' as result;
SELECT '👥 Admin Login: <EMAIL>, <EMAIL>, <EMAIL>' as admin_logins;
SELECT '🚗 Driver Login: <EMAIL>, <EMAIL>, <EMAIL>' as driver_logins;
SELECT '👨‍👩‍👧‍👦 Family Login: <EMAIL>, <EMAIL>, <EMAIL>' as family_logins;
