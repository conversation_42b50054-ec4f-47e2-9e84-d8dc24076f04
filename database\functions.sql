-- DITIB Funeral App - Complete Database Schema
-- Based on DATABASE-SYSTEM.md specifications
-- Version: 4.0 - Fully aligned with DATABASE-SYSTEM.md

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Drop existing tables if they exist (for clean redevelopment)
DROP TABLE IF EXISTS public.lookup_values CASCADE;
DROP TABLE IF EXISTS public.notifications CASCADE;
DROP TABLE IF EXISTS public.documents CASCADE;
DROP TABLE IF EXISTS public.task_status_history CASCADE;
DROP TABLE IF EXISTS public.tasks CASCADE;
DROP TABLE IF EXISTS public.cases CASCADE;
DROP TABLE IF EXISTS public.deceased CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- 1. USERS TABLE (ADMIN · DRIVER · FAMILY)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('ADMIN', 'DRIVER', 'FAMILY')),
  full_name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  phone TEXT,
  status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ON_LEAVE', 'INACTIVE')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. DECEASED TABLE (DITIB senkron - salt-okunur)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.deceased (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ditib_member_id TEXT UNIQUE,
  full_name TEXT NOT NULL,
  nationality TEXT,
  gender TEXT,
  date_of_death TIMESTAMP WITH TIME ZONE NOT NULL,
  place_of_death TEXT,
  place_of_burial TEXT,
  family_name TEXT,
  family_email TEXT,
  family_phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CASES TABLE (cenaze vakası)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.cases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  deceased_id UUID REFERENCES public.deceased(id) ON DELETE CASCADE,
  family_user_id UUID REFERENCES public.users(id), -- role=FAMILY
  status TEXT DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'CANCELLED')),
  burial_type TEXT CHECK (burial_type IN ('DE', 'TR')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. TASKS TABLE (süreç adımları - admin atar)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  assignee_id UUID REFERENCES public.users(id) ON DELETE SET NULL, -- DRIVER (NULL = henüz atanmamış)
  task_type TEXT NOT NULL CHECK (task_type IN ('PICK_UP_FROM_MORGUE', 'TO_AIRPORT', 'TO_CONSULATE', 'DELIVERED')),
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACTIVE', 'DONE', 'FAILED', 'OVERDUE')),
  sub_status TEXT CHECK (sub_status IN ('PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'ON_HOLD')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT
);

-- 5. TASK_STATUS_HISTORY TABLE (denetim izi)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.task_status_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
  actor_id UUID REFERENCES public.users(id), -- ADMIN/DRIVER
  old_status TEXT,
  new_status TEXT,
  old_sub_status TEXT,
  new_sub_status TEXT,
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. DOCUMENTS TABLE (evrak meta)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  doc_type TEXT NOT NULL CHECK (doc_type IN ('DEATH_CERT', 'FORMUL_C', 'CARGO_WAYBILL', 'PHOTO')),
  storage_path TEXT NOT NULL,
  uploaded_by UUID REFERENCES public.users(id), -- ADMIN/DRIVER
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


-- 7. NOTIFICATIONS TABLE (push / SMS / email logu)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID REFERENCES public.cases(id),
  channel TEXT NOT NULL CHECK (channel IN ('PUSH', 'SMS', 'EMAIL')),
  to_address TEXT NOT NULL, -- recipient
  template TEXT NOT NULL, -- template code
  sent_at TIMESTAMP WITH TIME ZONE,
  status TEXT CHECK (status IN ('SENT', 'FAIL'))
);

-- 8. LOOKUP_VALUES TABLE (kod sözlüğü - reserve future)
-- Based on DATABASE-SYSTEM.md specification
CREATE TABLE public.lookup_values (
  id SERIAL PRIMARY KEY,
  category TEXT NOT NULL,
  code TEXT NOT NULL,
  label_tr TEXT NOT NULL,
  label_de TEXT,
  UNIQUE(category, code)
);

-- INDEXES for performance optimization
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_status ON public.users(status);

CREATE INDEX idx_deceased_ditib_member ON public.deceased(ditib_member_id);
CREATE INDEX idx_deceased_death_date ON public.deceased(date_of_death);

CREATE INDEX idx_cases_deceased ON public.cases(deceased_id);
CREATE INDEX idx_cases_family_user ON public.cases(family_user_id);
CREATE INDEX idx_cases_status ON public.cases(status);
CREATE INDEX idx_cases_burial_type ON public.cases(burial_type);

CREATE INDEX idx_tasks_case ON public.tasks(case_id);
CREATE INDEX idx_tasks_assignee ON public.tasks(assignee_id);
CREATE INDEX idx_tasks_status ON public.tasks(status);
CREATE INDEX idx_tasks_type ON public.tasks(task_type);
CREATE INDEX idx_tasks_scheduled ON public.tasks(scheduled_at);

CREATE INDEX idx_task_history_task ON public.task_status_history(task_id);
CREATE INDEX idx_task_history_actor ON public.task_status_history(actor_id);
CREATE INDEX idx_task_history_changed ON public.task_status_history(changed_at);

CREATE INDEX idx_documents_case ON public.documents(case_id);
CREATE INDEX idx_documents_type ON public.documents(doc_type);
CREATE INDEX idx_documents_uploaded_by ON public.documents(uploaded_by);

CREATE INDEX idx_notifications_case ON public.notifications(case_id);
CREATE INDEX idx_notifications_channel ON public.notifications(channel);
CREATE INDEX idx_notifications_status ON public.notifications(status);

CREATE INDEX idx_lookup_category ON public.lookup_values(category);
CREATE INDEX idx_lookup_code ON public.lookup_values(code);

-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deceased ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lookup_values ENABLE ROW LEVEL SECURITY;

-- Success message
SELECT '✅ Complete database schema created successfully according to DATABASE-SYSTEM.md!' as result;
