-- OPTIMIZE EDİLMİŞ VERİTABANI TABLO YAPILARI
-- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ve Belgeler Sayfalarında Kullanılan Veri Yapılarına Göre Tasarlanmıştır
-- Tarih: 2024-01-20
-- Versiyon: 1.0

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. USERS TABLE (Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('ADMIN', 'DRIVER', 'FAMILY')),
  full_name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  phone TEXT,
  status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ON_LEAVE', 'INACTIVE')),
  
  -- Ek kullanıcı bilgileri
  profile_image_url TEXT,
  last_login_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. DECEASED TABLE (Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.deceased (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Temel bilgiler
  ditib_member_id TEXT UNIQUE,
  full_name TEXT NOT NULL,
  nationality TEXT DEFAULT 'Türkiye',
  gender TEXT CHECK (gender IN ('MALE', 'FEMALE')),
  
  -- Tarih bilgileri
  date_of_birth DATE,
  date_of_death TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Konum bilgileri
  place_of_death TEXT,
  place_of_burial TEXT,
  burial_location_latitude DECIMAL(10, 8),
  burial_location_longitude DECIMAL(11, 8),
  
  -- Aile iletişim bilgileri
  family_contact_name TEXT,
  family_email TEXT,
  family_phone TEXT,
  family_address TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CASES TABLE (Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.cases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- İlişkiler
  deceased_id UUID REFERENCES public.deceased(id) ON DELETE CASCADE,
  family_user_id UUID REFERENCES public.users(id),
  assigned_driver_id UUID REFERENCES public.users(id),
  
  -- Vaka durumu
  status TEXT DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'IN_PROGRESS', 'COMPLETED', 'CLOSED', 'CANCELLED')),
  priority TEXT DEFAULT 'MEDIUM' CHECK (priority IN ('URGENT', 'HIGH', 'MEDIUM', 'LOW')),
  
  -- Cenaze türü ve detayları
  burial_type TEXT CHECK (burial_type IN ('DE', 'TR', 'TRADITIONAL', 'MODERN')),
  burial_ceremony_type TEXT CHECK (burial_ceremony_type IN ('RELIGIOUS', 'CIVIL', 'MIXED')),
  
  -- İlerleme takibi
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  estimated_completion_date DATE,
  
  -- Görev sayıları (denormalized for performance)
  total_tasks_count INTEGER DEFAULT 0,
  completed_tasks_count INTEGER DEFAULT 0,
  pending_tasks_count INTEGER DEFAULT 0,
  
  -- Notlar
  notes TEXT,
  special_instructions TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. TASKS TABLE (Tamamen Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- İlişkiler
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  assignee_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
  created_by_id UUID REFERENCES public.users(id),
  
  -- Görev bilgileri
  task_type TEXT NOT NULL CHECK (task_type IN (
    'PICK_UP_FROM_MORGUE', 'TO_AIRPORT', 'TO_CONSULATE', 'DELIVERED',
    'DOCUMENT_DELIVERY', 'FAMILY_MEETING', 'HOSPITAL_TRANSFER',
    'BURIAL_PREPARATION', 'CEREMONY_SETUP', 'TRANSPORT'
  )),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Durum ve öncelik
  status TEXT DEFAULT 'PENDING' CHECK (status IN (
    'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'OVERDUE', 'CANCELLED'
  )),
  sub_status TEXT CHECK (sub_status IN (
    'PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'ON_HOLD', 'WAITING_APPROVAL',
    'READY_FOR_PICKUP', 'ARRIVED_AT_DESTINATION'
  )),
  priority TEXT DEFAULT 'MEDIUM' CHECK (priority IN (
    'URGENT', 'HIGH', 'MEDIUM', 'LOW'
  )),
  
  -- Zaman bilgileri
  scheduled_at TIMESTAMP WITH TIME ZONE,
  due_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  estimated_duration_minutes INTEGER,
  actual_duration_minutes INTEGER,
  
  -- Konum bilgileri
  location VARCHAR(500),
  pickup_address TEXT,
  delivery_address TEXT,
  pickup_latitude DECIMAL(10, 8),
  pickup_longitude DECIMAL(11, 8),
  delivery_latitude DECIMAL(10, 8),
  delivery_longitude DECIMAL(11, 8),
  
  -- İlerleme takibi
  progress_percentage INTEGER DEFAULT 0 CHECK (
    progress_percentage >= 0 AND progress_percentage <= 100
  ),
  
  -- Notlar ve feedback
  notes TEXT,
  completion_notes TEXT,
  driver_feedback TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. DOCUMENTS TABLE (Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- İlişkiler
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  task_id UUID REFERENCES public.tasks(id) ON DELETE SET NULL,
  uploaded_by_id UUID REFERENCES public.users(id),
  
  -- Belge bilgileri
  document_type TEXT NOT NULL CHECK (document_type IN (
    'DEATH_CERT', 'FORMUL_C', 'CARGO_WAYBILL', 'PHOTO',
    'DOCUMENT_PHOTO', 'TRANSPORT_DOCUMENT', 'NOTES',
    'FAMILY_CONSENT', 'MEDICAL_REPORT', 'BURIAL_PERMIT'
  )),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Dosya bilgileri
  file_name VARCHAR(255) NOT NULL,
  file_size_bytes BIGINT,
  file_mime_type VARCHAR(100),
  storage_path TEXT NOT NULL,
  file_count INTEGER DEFAULT 1,
  
  -- Durum takibi
  status TEXT DEFAULT 'PENDING' CHECK (status IN (
    'PENDING', 'APPROVED', 'REJECTED', 'UNDER_REVIEW'
  )),
  reviewed_by_id UUID REFERENCES public.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. TASK_STATUS_HISTORY TABLE (Audit Trail)
-- =====================================================
CREATE TABLE public.task_status_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
  actor_id UUID REFERENCES public.users(id),
  
  -- Durum değişiklikleri
  old_status TEXT,
  new_status TEXT,
  old_sub_status TEXT,
  new_sub_status TEXT,
  old_progress INTEGER,
  new_progress INTEGER,
  
  -- Konum bilgisi (değişiklik anında)
  location_latitude DECIMAL(10, 8),
  location_longitude DECIMAL(11, 8),
  
  -- Notlar
  change_reason TEXT,
  notes TEXT,
  
  -- Metadata
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. NOTIFICATIONS TABLE (Optimize Edilmiş)
-- =====================================================
CREATE TABLE public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- İlişkiler
  case_id UUID REFERENCES public.cases(id),
  task_id UUID REFERENCES public.tasks(id),
  user_id UUID REFERENCES public.users(id),
  
  -- Bildirim bilgileri
  notification_type TEXT NOT NULL CHECK (notification_type IN (
    'TASK_ASSIGNED', 'TASK_COMPLETED', 'TASK_OVERDUE', 'CASE_UPDATED',
    'DOCUMENT_UPLOADED', 'DOCUMENT_APPROVED', 'DOCUMENT_REJECTED',
    'FAMILY_MESSAGE', 'SYSTEM_ALERT'
  )),
  channel TEXT NOT NULL CHECK (channel IN ('PUSH', 'SMS', 'EMAIL', 'IN_APP')),
  
  -- İçerik
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  to_address TEXT NOT NULL,
  
  -- Durum
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED')),
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. LOOKUP_VALUES TABLE (Kod Sözlüğü)
-- =====================================================
CREATE TABLE public.lookup_values (
  id SERIAL PRIMARY KEY,
  category TEXT NOT NULL,
  code TEXT NOT NULL,
  label_tr TEXT NOT NULL,
  label_de TEXT,
  label_en TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(category, code)
);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_users_updated_at 
BEFORE UPDATE ON public.users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deceased_updated_at 
BEFORE UPDATE ON public.deceased
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cases_updated_at 
BEFORE UPDATE ON public.cases
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at 
BEFORE UPDATE ON public.tasks
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at 
BEFORE UPDATE ON public.documents
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INDEXES (Performance Optimization)
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_status ON public.users(status);
CREATE INDEX idx_users_role_status ON public.users(role, status);

-- Deceased indexes
CREATE INDEX idx_deceased_ditib_member ON public.deceased(ditib_member_id);
CREATE INDEX idx_deceased_death_date ON public.deceased(date_of_death);
CREATE INDEX idx_deceased_family_contact ON public.deceased(family_contact_name);

-- Cases indexes
CREATE INDEX idx_cases_deceased ON public.cases(deceased_id);
CREATE INDEX idx_cases_family_user ON public.cases(family_user_id);
CREATE INDEX idx_cases_driver ON public.cases(assigned_driver_id);
CREATE INDEX idx_cases_status ON public.cases(status);
CREATE INDEX idx_cases_priority ON public.cases(priority);
CREATE INDEX idx_cases_burial_type ON public.cases(burial_type);
CREATE INDEX idx_cases_status_priority ON public.cases(status, priority);
CREATE INDEX idx_cases_created_at ON public.cases(created_at);

-- Tasks indexes (Critical for performance)
CREATE INDEX idx_tasks_case ON public.tasks(case_id);
CREATE INDEX idx_tasks_assignee ON public.tasks(assignee_id);
CREATE INDEX idx_tasks_status ON public.tasks(status);
CREATE INDEX idx_tasks_type ON public.tasks(task_type);
CREATE INDEX idx_tasks_priority ON public.tasks(priority);
CREATE INDEX idx_tasks_scheduled ON public.tasks(scheduled_at);
CREATE INDEX idx_tasks_due_at ON public.tasks(due_at);
CREATE INDEX idx_tasks_assignee_status ON public.tasks(assignee_id, status);
CREATE INDEX idx_tasks_case_status ON public.tasks(case_id, status);
CREATE INDEX idx_tasks_priority_due_at ON public.tasks(priority DESC, due_at ASC);
CREATE INDEX idx_tasks_progress ON public.tasks(progress_percentage);
CREATE INDEX idx_tasks_location ON public.tasks(pickup_latitude, pickup_longitude);
CREATE INDEX idx_tasks_delivery_location ON public.tasks(delivery_latitude, delivery_longitude);

-- Documents indexes
CREATE INDEX idx_documents_case ON public.documents(case_id);
CREATE INDEX idx_documents_task ON public.documents(task_id);
CREATE INDEX idx_documents_type ON public.documents(document_type);
CREATE INDEX idx_documents_status ON public.documents(status);
CREATE INDEX idx_documents_uploaded_by ON public.documents(uploaded_by_id);
CREATE INDEX idx_documents_case_type ON public.documents(case_id, document_type);

-- Task history indexes
CREATE INDEX idx_task_history_task ON public.task_status_history(task_id);
CREATE INDEX idx_task_history_actor ON public.task_status_history(actor_id);
CREATE INDEX idx_task_history_changed ON public.task_status_history(changed_at);

-- Notifications indexes
CREATE INDEX idx_notifications_case ON public.notifications(case_id);
CREATE INDEX idx_notifications_task ON public.notifications(task_id);
CREATE INDEX idx_notifications_user ON public.notifications(user_id);
CREATE INDEX idx_notifications_type ON public.notifications(notification_type);
CREATE INDEX idx_notifications_channel ON public.notifications(channel);
CREATE INDEX idx_notifications_status ON public.notifications(status);
CREATE INDEX idx_notifications_user_status ON public.notifications(user_id, status);

-- Lookup values indexes
CREATE INDEX idx_lookup_category ON public.lookup_values(category);
CREATE INDEX idx_lookup_code ON public.lookup_values(code);
CREATE INDEX idx_lookup_active ON public.lookup_values(is_active);

-- =====================================================
-- VIEWS (Commonly Used Queries)
-- =====================================================

-- Driver Tasks View
CREATE VIEW driver_tasks_view AS
SELECT 
  t.id,
  t.case_id,
  t.title,
  t.description,
  t.task_type,
  t.status,
  t.sub_status,
  t.priority,
  t.scheduled_at,
  t.due_at,
  t.location,
  t.progress_percentage,
  t.estimated_duration_minutes,
  c.status as case_status,
  d.full_name as deceased_name,
  d.family_contact_name,
  d.family_phone,
  u.full_name as assignee_name
FROM tasks t
JOIN cases c ON t.case_id = c.id
JOIN deceased d ON c.deceased_id = d.id
LEFT JOIN users u ON t.assignee_id = u.id;

-- Case Summary View
CREATE VIEW case_summary_view AS
SELECT 
  c.id,
  c.status,
  c.priority,
  c.burial_type,
  c.progress_percentage,
  c.total_tasks_count,
  c.completed_tasks_count,
  c.pending_tasks_count,
  c.estimated_completion_date,
  d.full_name as deceased_name,
  d.date_of_death,
  d.family_contact_name,
  d.family_phone,
  d.family_email,
  u_family.full_name as family_user_name,
  u_driver.full_name as assigned_driver_name,
  c.created_at,
  c.updated_at
FROM cases c
JOIN deceased d ON c.deceased_id = d.id
LEFT JOIN users u_family ON c.family_user_id = u_family.id
LEFT JOIN users u_driver ON c.assigned_driver_id = u_driver.id;

-- Document Summary View
CREATE VIEW document_summary_view AS
SELECT 
  doc.id,
  doc.case_id,
  doc.task_id,
  doc.document_type,
  doc.title,
  doc.file_name,
  doc.file_size_bytes,
  doc.file_count,
  doc.status,
  doc.rejection_reason,
  doc.created_at,
  c.status as case_status,
  d.full_name as deceased_name,
  u_uploader.full_name as uploaded_by_name,
  u_reviewer.full_name as reviewed_by_name
FROM documents doc
JOIN cases c ON doc.case_id = c.id
JOIN deceased d ON c.deceased_id = d.id
LEFT JOIN users u_uploader ON doc.uploaded_by_id = u_uploader.id
LEFT JOIN users u_reviewer ON doc.reviewed_by_id = u_reviewer.id;

-- =====================================================
-- FUNCTIONS (Business Logic)
-- =====================================================

-- Update case task counts
CREATE OR REPLACE FUNCTION update_case_task_counts(case_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE cases SET
    total_tasks_count = (SELECT COUNT(*) FROM tasks WHERE case_id = case_uuid),
    completed_tasks_count = (SELECT COUNT(*) FROM tasks WHERE case_id = case_uuid AND status = 'COMPLETED'),
    pending_tasks_count = (SELECT COUNT(*) FROM tasks WHERE case_id = case_uuid AND status IN ('PENDING', 'IN_PROGRESS'))
  WHERE id = case_uuid;
END;
$$ LANGUAGE plpgsql;

-- Calculate case progress
CREATE OR REPLACE FUNCTION calculate_case_progress(case_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  total_tasks INTEGER;
  completed_tasks INTEGER;
  progress INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_tasks FROM tasks WHERE case_id = case_uuid;
  SELECT COUNT(*) INTO completed_tasks FROM tasks WHERE case_id = case_uuid AND status = 'COMPLETED';
  
  IF total_tasks = 0 THEN
    RETURN 0;
  END IF;
  
  progress := (completed_tasks * 100) / total_tasks;
  
  UPDATE cases SET progress_percentage = progress WHERE id = case_uuid;
  
  RETURN progress;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deceased ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lookup_values ENABLE ROW LEVEL SECURITY;

-- Success message
SELECT '✅ Optimize edilmiş veritabanı şeması başarıyla oluşturuldu!' as result;