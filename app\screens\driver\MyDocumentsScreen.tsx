import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from '../../i18n/useTranslation';
import { useAlertHelpers } from '../../hooks/useAlert';
import { useDocumentAlerts } from '../../hooks/useDocumentAlerts';

interface MyDocumentsScreenProps {
  user: User;
  onBack: () => void;
}

const MyDocumentsScreen = ({ user, onBack }: MyDocumentsScreenProps) => {
  const { t } = useTranslation();
  const { showInfo, showConfirm } = useAlertHelpers();
  const { showDocumentUploadAlert } = useDocumentAlerts();
  const [selectedFilter, setSelectedFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');

  // Mock data - gerçek uygulamada API'den gelecek
  const documents = [
    {
      id: '1',
      name: 'Cenaze Fotoğrafları - Ahmet Yılmaz',
      type: 'PHOTO',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-20T14:30:00Z',
      file_size: '3.2 MB',
      status: 'APPROVED',
      file_count: 5,
      description: 'Cenaze töreni ve defin işlemi fotoğrafları',
    },
    {
      id: '2',
      name: 'Belge Teslim Fotoğrafı',
      type: 'DOCUMENT_PHOTO',
      case_name: 'Fatma Şahin Vakası',
      uploaded_at: '2024-01-20T11:15:00Z',
      file_size: '1.8 MB',
      status: 'PENDING',
      file_count: 2,
      description: 'Nüfus müdürlüğüne belge teslimi',
    },
    {
      id: '3',
      name: 'Hastane Nakil Belgesi',
      type: 'TRANSPORT_DOCUMENT',
      case_name: 'Zeynep Arslan Vakası',
      uploaded_at: '2024-01-19T16:45:00Z',
      file_size: '0.9 MB',
      status: 'APPROVED',
      file_count: 1,
      description: 'Hastaneden morgue nakil belgesi',
    },
    {
      id: '4',
      name: 'Aile Görüşme Notları',
      type: 'NOTES',
      case_name: 'Ali Kaya Vakası',
      uploaded_at: '2024-01-19T09:20:00Z',
      file_size: '0.3 MB',
      status: 'REJECTED',
      file_count: 1,
      description: 'Aile ile yapılan görüşme notları',
      rejection_reason: 'Eksik bilgi - aile iletişim bilgileri eksik',
    },
  ];

  const typeColors = {
    PHOTO: '#9B59B6',
    DOCUMENT_PHOTO: '#2ECC71',
    TRANSPORT_DOCUMENT: '#F39C12',
    NOTES: '#0D3B66',
  };

  const typeLabels = {
    PHOTO: 'Fotoğraf',
    DOCUMENT_PHOTO: 'Belge Fotoğrafı',
    TRANSPORT_DOCUMENT: 'Nakil Belgesi',
    NOTES: 'Notlar',
  };

  const statusColors = {
    PENDING: '#F39C12',
    APPROVED: '#2ECC71',
    REJECTED: '#E74C3C',
  };

  const statusLabels = {
    PENDING: t('tasks.pending'),
    APPROVED: 'Onaylandı',
    REJECTED: 'Reddedildi',
  };

  const filteredDocuments = documents.filter(doc =>
    selectedFilter === 'ALL' || doc.status === selectedFilter
  );

  const handleUploadDocument = () => {
    showDocumentUploadAlert('driver', {
      onTakePhoto: () => console.log('Camera opened'),
      onSelectFromGallery: () => console.log('Gallery opened'),
      onCancel: () => console.log('Upload cancelled')
    });
  };

  const handleViewDocument = (documentId: string) => {
    showInfo(t('alerts.documentView'), t('alerts.documentViewFeature', { id: documentId }));
  };

  const handleDeleteDocument = (documentId: string, documentName: string) => {
    showConfirm(
      t('common.delete'),
      `"${documentName}" belgesini silmek istediğinizden emin misiniz?`,
      () => console.log(`Document ${documentId} deleted`),
      undefined,
      t('common.delete'),
      t('common.cancel')
    );
  };

  const handleRetryUpload = (documentId: string) => {
    showConfirm(
      'Tekrar Yükle',
      'Bu belgeyi düzenleyip tekrar yüklemek istediğinizden emin misiniz?',
      () => console.log(`Retrying upload for ${documentId}`),
      undefined,
      'Tekrar Yükle',
      t('common.cancel')
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDocumentStats = () => {
    const pending = documents.filter(d => d.status === 'PENDING').length;
    const approved = documents.filter(d => d.status === 'APPROVED').length;
    const rejected = documents.filter(d => d.status === 'REJECTED').length;
    return { pending, approved, rejected };
  };

  const stats = getDocumentStats();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#2ECC71" />
        </TouchableOpacity>
        <Text style={styles.title}>{t('navigation.documents')}</Text>
        <TouchableOpacity onPress={handleUploadDocument} style={styles.uploadButton}>
          <Ionicons name="add-circle" size={24} color="#2ECC71" />
        </TouchableOpacity>
      </View>

      {/* Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.pending}</Text>
          <Text style={styles.statLabel}>{t('tasks.pending')}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.approved}</Text>
          <Text style={styles.statLabel}>Onaylanan</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.rejected}</Text>
          <Text style={styles.statLabel}>Reddedilen</Text>
        </View>
      </View>

      {/* Filter */}
      <View style={styles.filterSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {(['ALL', 'PENDING', 'APPROVED', 'REJECTED'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter === 'ALL' ? 'Tümü' : statusLabels[filter]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Documents List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredDocuments.map((document) => (
          <TouchableOpacity
            key={document.id}
            style={[
              styles.documentCard,
              document.status === 'REJECTED' && styles.rejectedCard
            ]}
            onPress={() => handleViewDocument(document.id)}
          >
            <View style={styles.documentHeader}>
              <View style={styles.documentIcon}>
                <Ionicons
                  name={
                    document.type === 'PHOTO' ? 'images' :
                    document.type === 'DOCUMENT_PHOTO' ? 'document-text' :
                    document.type === 'TRANSPORT_DOCUMENT' ? 'car' : 'create'
                  }
                  size={24}
                  color={typeColors[document.type as keyof typeof typeColors]}
                />
              </View>
              <View style={styles.documentInfo}>
                <Text style={styles.documentName}>{document.name}</Text>
                <Text style={styles.caseName}>{document.case_name}</Text>
                <Text style={styles.documentDescription}>{document.description}</Text>
                <View style={styles.documentMeta}>
                  <View style={[
                    styles.typeBadge,
                    { backgroundColor: typeColors[document.type as keyof typeof typeColors] }
                  ]}>
                    <Text style={styles.typeText}>
                      {typeLabels[document.type as keyof typeof typeLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[document.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[document.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Document Details */}
            <View style={styles.documentDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="time" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {formatDate(document.uploaded_at)}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="folder" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{document.file_size}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="images" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {document.file_count} dosya
                  </Text>
                </View>
              </View>

              {/* Rejection Reason */}
              {document.status === 'REJECTED' && document.rejection_reason && (
                <View style={styles.rejectionReason}>
                  <Ionicons name="warning" size={16} color="#E74C3C" />
                  <Text style={styles.rejectionText}>{document.rejection_reason}</Text>
                </View>
              )}

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.viewButton}
                  onPress={() => handleViewDocument(document.id)}
                >
                  <Ionicons name="eye" size={16} color="#2ECC71" />
                  <Text style={styles.viewButtonText}>Görüntüle</Text>
                </TouchableOpacity>

                {document.status === 'REJECTED' && (
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={() => handleRetryUpload(document.id)}
                  >
                    <Ionicons name="refresh" size={16} color="#F39C12" />
                    <Text style={styles.retryButtonText}>Tekrar Yükle</Text>
                  </TouchableOpacity>
                )}

                {document.status === 'PENDING' && (
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => handleDeleteDocument(document.id, document.name)}
                  >
                    <Ionicons name="trash" size={16} color="#E74C3C" />
                    <Text style={styles.deleteButtonText}>Sil</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}

        {filteredDocuments.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Belge bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'ALL'
                ? 'Henüz yüklediğiniz belge bulunmuyor.'
                : `${statusLabels[selectedFilter]} durumunda belge bulunmuyor.`
              }
            </Text>
            <TouchableOpacity style={styles.uploadEmptyButton} onPress={handleUploadDocument}>
              <Ionicons name="camera" size={20} color="#FFFFFF" />
              <Text style={styles.uploadEmptyButtonText}>İlk Belgenizi Yükleyin</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  uploadButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2ECC71',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    color: '#8E9297',
  },
  filterSection: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#2ECC71',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  documentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  rejectedCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  documentHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  caseName: {
    fontSize: 12,
    color: '#2ECC71',
    fontWeight: '500',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  documentMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  documentDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  rejectionReason: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF5F5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  rejectionText: {
    fontSize: 12,
    color: '#E74C3C',
    marginLeft: 8,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#2ECC71',
  },
  viewButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2ECC71',
    marginLeft: 4,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F39C12',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
  },
  retryButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#E74C3C',
  },
  deleteButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#E74C3C',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
    paddingHorizontal: 32,
    marginBottom: 24,
  },
  uploadEmptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2ECC71',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  uploadEmptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});

export default MyDocumentsScreen;
