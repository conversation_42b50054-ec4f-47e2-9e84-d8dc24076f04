import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  Alert,
  SafeAreaView,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

interface TasksManagementScreenProps {
  onBack: () => void;
}

interface TaskData {
  id: string;
  title: string;
  description: string;
  case_name: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  assigned_to: string;
  due_date: string;
  created_at: string;
  estimated_duration: string;
}

const TasksManagementScreen: React.FC<TasksManagementScreenProps> = ({ onBack }) => {
  const { t } = useTranslation();
  const [tasks, setTasks] = useState<TaskData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'>('ALL');

  const statusColors = {
    PENDING: '#F39C12',
    IN_PROGRESS: '#2ECC71',
    COMPLETED: '#0D3B66',
  };

  const statusLabels = {
    PENDING: 'Bekliyor',
    IN_PROGRESS: 'Devam Ediyor',
    COMPLETED: 'Tamamlandı',
  };

  const priorityColors = {
    HIGH: '#E74C3C',
    MEDIUM: '#F39C12',
    LOW: '#2ECC71',
  };

  const priorityLabels = {
    HIGH: 'Yüksek',
    MEDIUM: 'Orta',
    LOW: 'Düşük',
  };

  useEffect(() => {
    loadTasks();
  }, []);

  const loadTasks = async () => {
    try {
      setLoading(true);
      // Mock data for now
      const mockTasks: TaskData[] = [
        {
          id: '1',
          title: 'Morgdan Alma',
          description: 'Cenaze morgdan alınacak',
          case_name: 'Ahmet Yılmaz Vakası',
          status: 'PENDING',
          priority: 'HIGH',
          assigned_to: 'Mehmet Sürücü',
          due_date: '2024-01-15',
          created_at: '2024-01-10',
          estimated_duration: '2 saat',
        },
        {
          id: '2',
          title: 'Havaalanına Götürme',
          description: 'Cenaze havaalanına götürülecek',
          case_name: 'Fatma Demir Vakası',
          status: 'IN_PROGRESS',
          priority: 'MEDIUM',
          assigned_to: 'Ali Sürücü',
          due_date: '2024-01-16',
          created_at: '2024-01-11',
          estimated_duration: '3 saat',
        },
      ];
      setTasks(mockTasks);
    } catch (error) {
      console.error('Error loading tasks:', error);
      Alert.alert(t('common.error'), t('alerts.loadingError'));
    } finally {
      setLoading(false);
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.case_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.assigned_to.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'ALL' || task.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const isOverdue = (dueDate: string): boolean => {
    return new Date(dueDate) < new Date();
  };

  const handleAddTask = () => {
    Alert.alert(t('alerts.newTask'), t('alerts.newTaskFeature'));
  };

  const handleViewTask = (taskId: string) => {
    Alert.alert(t('alerts.taskDetails'), t('alerts.taskDetail', { id: taskId }));
  };

  const handleReassignTask = (taskId: string) => {
    Alert.alert(t('alerts.taskDetails'), t('alerts.taskReassign', { id: taskId }));
  };

  const rightComponent = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddTask}
      activeOpacity={0.8}
    >
      <Ionicons name="add" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>Görev Yönetimi</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddTask}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={16} color="#8E9297" />
          <TextInput
            style={styles.searchInput}
            placeholder="Görev ara..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#8E9297"
          />
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          {(['ALL', 'PENDING', 'IN_PROGRESS', 'COMPLETED'] as const).map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                selectedStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setSelectedStatus(status)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedStatus === status && styles.filterButtonTextActive
              ]}>
                {status === 'ALL' ? 'Tümü' : statusLabels[status]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tasks List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.statsRow}>
          <Text style={styles.statsText}>
            {filteredTasks.length} görev bulundu
          </Text>
        </View>

      {filteredTasks.map((task) => (
        <TouchableOpacity
          key={task.id}
          style={[
            styles.taskCard,
            isOverdue(task.due_date) && styles.overdueCard
          ]}
          onPress={() => handleViewTask(task.id)}
        >
          <View style={styles.taskHeader}>
            <View style={styles.taskInfo}>
              <View style={styles.taskTitleRow}>
                <Text style={styles.taskTitle}>{task.title}</Text>
                {isOverdue(task.due_date) && (
                  <Ionicons name="warning" size={16} color="#E74C3C" />
                )}
              </View>
              <Text style={styles.taskDescription}>{task.description}</Text>
              <Text style={styles.caseName}>{task.case_name}</Text>

              <View style={styles.taskMeta}>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: statusColors[task.status as keyof typeof statusColors] }
                ]}>
                  <Text style={styles.statusText}>
                    {statusLabels[task.status as keyof typeof statusLabels]}
                  </Text>
                </View>
                <View style={[
                  styles.priorityBadge,
                  { backgroundColor: priorityColors[task.priority as keyof typeof priorityColors] }
                ]}>
                  <Text style={styles.priorityText}>
                    {priorityLabels[task.priority as keyof typeof priorityLabels]}
                  </Text>
                </View>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#8E9297" />
          </View>

          {/* Task Details */}
          <View style={styles.taskDetails}>
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Ionicons name="person" size={14} color="#8E9297" />
                <Text style={styles.detailText}>{task.assigned_to}</Text>
              </View>
              <View style={styles.detailItem}>
                <Ionicons name="time" size={14} color="#8E9297" />
                <Text style={styles.detailText}>{task.estimated_duration}</Text>
              </View>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Ionicons name="calendar" size={14} color="#8E9297" />
                <Text style={[
                  styles.detailText,
                  isOverdue(task.due_date) && styles.overdueText
                ]}>
                  {new Date(task.due_date).toLocaleDateString('tr-TR')}
                </Text>
              </View>
              <View style={styles.detailItem}>
                <Ionicons name="create" size={14} color="#8E9297" />
                <Text style={styles.detailText}>
                  {new Date(task.created_at).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            </View>

            {task.status !== 'COMPLETED' && (
              <TouchableOpacity
                style={styles.reassignButton}
                onPress={() => handleReassignTask(task.id)}
              >
                <Ionicons name="swap-horizontal" size={16} color="#0D3B66" />
                <Text style={styles.reassignButtonText}>Yeniden Ata</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      ))}

      {filteredTasks.length === 0 && (
        <View style={styles.emptyState}>
          <Ionicons name="checkmark-circle-outline" size={64} color="#E1E8ED" />
          <Text style={styles.emptyStateTitle}>Görev bulunamadı</Text>
          <Text style={styles.emptyStateText}>
            Arama kriterlerinize uygun görev bulunmuyor.
          </Text>
        </View>
      )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0D3B66',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F8FA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#1C1C1E',
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    marginRight: 6,
  },
  filterButtonActive: {
    backgroundColor: '#0D3B66',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsRow: {
    paddingVertical: 12,
  },
  statsText: {
    fontSize: 14,
    color: '#8E9297',
    fontWeight: '500',
  },
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
    marginRight: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 4,
  },
  caseName: {
    fontSize: 12,
    color: '#0D3B66',
    fontWeight: '500',
    marginBottom: 8,
  },
  taskMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  taskDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  overdueText: {
    color: '#E74C3C',
    fontWeight: '600',
  },
  reassignButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#0D3B66',
  },
  reassignButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#0D3B66',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
  },
});

export default TasksManagementScreen;
