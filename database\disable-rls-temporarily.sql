-- DITIB Funeral App - Temporarily Disable RLS for Testing
-- Bu dosyayı Supabase SQL Editor'de çalıştırarak RLS'yi geçici olarak devre dışı bırakın

-- 1. Tüm RLS politikalarını devre dışı bırak
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.deceased DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_status_history DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.lookup_values DISABLE ROW LEVEL SECURITY;

-- 2. Mevcut RLS durumunu kontrol et
SELECT
  'RLS Status After Disable:' as info,
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
  AND tablename IN ('users', 'deceased', 'cases', 'tasks', 'documents', 'notifications')
ORDER BY tablename;

-- 3. Başarı mesajı
SELECT '✅ RLS temporarily disabled for testing!' as message;
SELECT 'All tables now allow unrestricted access.' as info;
SELECT 'Remember to re-enable RLS after testing!' as warning;
