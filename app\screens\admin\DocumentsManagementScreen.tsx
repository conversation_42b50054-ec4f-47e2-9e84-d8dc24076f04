import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useDocumentAlerts } from '../../hooks/useDocumentAlerts';

interface DocumentsManagementScreenProps {
  user: User;
  onBack: () => void;
}

const DocumentsManagementScreen = ({ user, onBack }: DocumentsManagementScreenProps) => {
  const { showDocumentUploadAlert } = useDocumentAlerts();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<'ALL' | 'DEATH_CERTIFICATE' | 'BURIAL_PERMIT' | 'PHOTO' | 'OTHER'>('ALL');

  // Mock data - gerçek uygulamada API'den gelecek
  const documents = [
    {
      id: '1',
      name: 'Ölüm Belgesi - Ahmet Yılmaz',
      type: 'DEATH_CERTIFICATE',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_by: 'Ali Kaya',
      uploaded_at: '2024-01-20T10:30:00Z',
      file_size: '2.4 MB',
      status: 'APPROVED',
    },
    {
      id: '2',
      name: 'Defin Ruhsatı - Fatma Şahin',
      type: 'BURIAL_PERMIT',
      case_name: 'Fatma Şahin Vakası',
      uploaded_by: 'Mehmet Demir',
      uploaded_at: '2024-01-19T14:15:00Z',
      file_size: '1.8 MB',
      status: 'PENDING',
    },
    {
      id: '3',
      name: 'Cenaze Fotoğrafları',
      type: 'PHOTO',
      case_name: 'Zeynep Arslan Vakası',
      uploaded_by: 'Ayşe Yılmaz',
      uploaded_at: '2024-01-18T16:45:00Z',
      file_size: '5.2 MB',
      status: 'APPROVED',
    },
    {
      id: '4',
      name: 'Kimlik Fotokopisi',
      type: 'OTHER',
      case_name: 'Ali Kaya Vakası',
      uploaded_by: 'Admin',
      uploaded_at: '2024-01-17T09:20:00Z',
      file_size: '0.8 MB',
      status: 'REJECTED',
    },
  ];

  const typeColors = {
    DEATH_CERTIFICATE: '#E74C3C',
    BURIAL_PERMIT: '#2ECC71',
    PHOTO: '#9B59B6',
    OTHER: '#F39C12',
  };

  const typeLabels = {
    DEATH_CERTIFICATE: 'Ölüm Belgesi',
    BURIAL_PERMIT: 'Defin Ruhsatı',
    PHOTO: 'Fotoğraf',
    OTHER: 'Diğer',
  };

  const statusColors = {
    PENDING: '#F39C12',
    APPROVED: '#2ECC71',
    REJECTED: '#E74C3C',
  };

  const statusLabels = {
    PENDING: 'Bekliyor',
    APPROVED: 'Onaylandı',
    REJECTED: 'Reddedildi',
  };

  const filteredDocuments = documents.filter(d => {
    const matchesSearch = d.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         d.case_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         d.uploaded_by.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedType === 'ALL' || d.type === selectedType;
    return matchesSearch && matchesType;
  });

  const handleUploadDocument = () => {
    showDocumentUploadAlert('admin', {
      onTakePhoto: () => console.log('Admin: Camera opened'),
      onSelectFromGallery: () => console.log('Admin: Gallery opened'),
      onCancel: () => console.log('Admin: Upload cancelled')
    });
  };

  const handleViewDocument = (documentId: string) => {
    Alert.alert('Belge Görüntüle', `Belge görüntüleme özelliği yakında eklenecek. ID: ${documentId}`);
  };

  const handleApproveDocument = (documentId: string) => {
    Alert.alert(
      'Belge Onayla',
      'Bu belgeyi onaylamak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Onayla', onPress: () => console.log(`Document ${documentId} approved`) },
      ]
    );
  };

  const handleRejectDocument = (documentId: string) => {
    Alert.alert(
      'Belge Reddet',
      'Bu belgeyi reddetmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Reddet', style: 'destructive', onPress: () => console.log(`Document ${documentId} rejected`) },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>Belge Yönetimi</Text>
        <TouchableOpacity onPress={handleUploadDocument} style={styles.addButton}>
          <Ionicons name="add-circle" size={24} color="#0D3B66" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={16} color="#8E9297" />
          <TextInput
            style={styles.searchInput}
            placeholder="Belge ara..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#8E9297"
          />
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          {(['ALL', 'DEATH_CERTIFICATE', 'BURIAL_PERMIT', 'PHOTO', 'OTHER'] as const).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.filterButton,
                selectedType === type && styles.filterButtonActive
              ]}
              onPress={() => setSelectedType(type)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedType === type && styles.filterButtonTextActive
              ]}>
                {type === 'ALL' ? 'Tümü' : typeLabels[type]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Documents List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.statsRow}>
          <Text style={styles.statsText}>
            {filteredDocuments.length} belge bulundu
          </Text>
        </View>

        {filteredDocuments.map((document) => (
          <TouchableOpacity
            key={document.id}
            style={styles.documentCard}
            onPress={() => handleViewDocument(document.id)}
          >
            <View style={styles.documentHeader}>
              <View style={styles.documentIcon}>
                <Ionicons
                  name={
                    document.type === 'PHOTO' ? 'image' :
                    document.type === 'DEATH_CERTIFICATE' ? 'document-text' :
                    document.type === 'BURIAL_PERMIT' ? 'clipboard' : 'document'
                  }
                  size={24}
                  color={typeColors[document.type as keyof typeof typeColors]}
                />
              </View>
              <View style={styles.documentInfo}>
                <Text style={styles.documentName}>{document.name}</Text>
                <Text style={styles.caseName}>{document.case_name}</Text>
                <View style={styles.documentMeta}>
                  <View style={[
                    styles.typeBadge,
                    { backgroundColor: typeColors[document.type as keyof typeof typeColors] }
                  ]}>
                    <Text style={styles.typeText}>
                      {typeLabels[document.type as keyof typeof typeLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[document.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[document.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#8E9297" />
            </View>

            {/* Document Details */}
            <View style={styles.documentDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="person" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{document.uploaded_by}</Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="folder" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{document.file_size}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="time" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {formatDate(document.uploaded_at)}
                  </Text>
                </View>
              </View>

              {document.status === 'PENDING' && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.approveButton}
                    onPress={() => handleApproveDocument(document.id)}
                  >
                    <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                    <Text style={styles.approveButtonText}>Onayla</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.rejectButton}
                    onPress={() => handleRejectDocument(document.id)}
                  >
                    <Ionicons name="close" size={16} color="#FFFFFF" />
                    <Text style={styles.rejectButtonText}>Reddet</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}

        {filteredDocuments.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Belge bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              Arama kriterlerinize uygun belge bulunmuyor.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F8FA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#1C1C1E',
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    marginRight: 6,
  },
  filterButtonActive: {
    backgroundColor: '#0D3B66',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  statsRow: {
    paddingVertical: 16,
  },
  statsText: {
    fontSize: 14,
    color: '#8E9297',
    fontWeight: '500',
  },
  documentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  documentHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  caseName: {
    fontSize: 12,
    color: '#0D3B66',
    fontWeight: '500',
    marginBottom: 8,
  },
  documentMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  documentDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
  },
  approveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2ECC71',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  approveButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  rejectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E74C3C',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  rejectButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
  },
});

export default DocumentsManagementScreen;
