-- DITIB Funeral App - Database Debug Check
-- Bu dosyayı çalıştırarak veri tabanının durumunu kontrol edebilirsiniz

-- 1. Ta<PERSON><PERSON><PERSON><PERSON><PERSON> varlığını kontrol et
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('users', 'deceased', 'cases', 'tasks', 'documents', 'notifications', 'lookup_values')
ORDER BY tablename;

-- 2. Users tablosu var mı ve içinde veri var mı?
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
    RAISE NOTICE '✅ Users tablosu mevcut';
    
    -- Users sayısını kontrol et
    PERFORM (SELECT COUNT(*) FROM public.users);
    RAISE NOTICE 'Users tablosunda % kayıt var', (SELECT COUNT(*) FROM public.users);
    
    -- Test kullanıcılarını listele
    RAISE NOTICE 'Test kullanıcıları:';
    FOR rec IN SELECT email, full_name, role FROM public.users LIMIT 5 LOOP
      RAISE NOTICE '  - %: % (%)', rec.email, rec.full_name, rec.role;
    END LOOP;
  ELSE
    RAISE NOTICE '❌ Users tablosu bulunamadı!';
  END IF;
END $$;

-- 3. Diğer tabloların durumu
DO $$
DECLARE
  table_name TEXT;
  row_count INTEGER;
BEGIN
  FOR table_name IN VALUES ('deceased'), ('cases'), ('tasks'), ('documents'), ('notifications'), ('lookup_values') LOOP
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = table_name) THEN
      EXECUTE format('SELECT COUNT(*) FROM public.%I', table_name) INTO row_count;
      RAISE NOTICE '✅ % tablosu: % kayıt', table_name, row_count;
    ELSE
      RAISE NOTICE '❌ % tablosu bulunamadı!', table_name;
    END IF;
  END LOOP;
END $$;

-- 4. Fonksiyonların varlığını kontrol et
SELECT 
  routine_name,
  routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('get_admin_user', 'assign_tasks', 'create_case', 'handle_task_status_change', 'create_next_task', 'close_case')
ORDER BY routine_name;

-- 5. Trigger'ların varlığını kontrol et
SELECT 
  trigger_name,
  event_object_table,
  action_timing,
  event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
  AND trigger_name LIKE 'trigger_%'
ORDER BY trigger_name;

-- 6. Test kullanıcıları detaylı kontrol
SELECT 
  'Test Users Check' as info,
  email,
  full_name,
  role,
  status,
  created_at
FROM public.users 
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ORDER BY email;

-- 7. Vaka ve görev durumu
SELECT 
  'Cases and Tasks Summary' as info,
  c.id as case_id,
  d.full_name as deceased_name,
  c.status as case_status,
  COUNT(t.id) as task_count
FROM public.cases c
LEFT JOIN public.deceased d ON c.deceased_id = d.id
LEFT JOIN public.tasks t ON t.case_id = c.id
GROUP BY c.id, d.full_name, c.status
ORDER BY c.created_at;

-- 8. RLS durumu
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('users', 'deceased', 'cases', 'tasks', 'documents', 'notifications')
ORDER BY tablename;

-- 9. Özet rapor
SELECT '=== DATABASE STATUS SUMMARY ===' as summary;
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') 
    THEN '✅ Schema OK' 
    ELSE '❌ Schema Missing' 
  END as schema_status;

SELECT 
  CASE 
    WHEN (SELECT COUNT(*) FROM public.users WHERE email = '<EMAIL>') > 0 
    THEN '✅ Test Data OK' 
    ELSE '❌ Test Data Missing' 
  END as test_data_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.routines WHERE routine_schema = 'public' AND routine_name = 'get_admin_user') 
    THEN '✅ Functions OK' 
    ELSE '❌ Functions Missing' 
  END as functions_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.triggers WHERE trigger_schema = 'public' AND trigger_name = 'trigger_assign_tasks') 
    THEN '✅ Triggers OK' 
    ELSE '❌ Triggers Missing' 
  END as triggers_status;

-- 10. Sonuç
SELECT '🔍 Debug check completed! Check the output above for any issues.' as result;
