// Supabase Configuration
// Bu değerleri Supabase Dashboard > Settings > API'den alın
export const SUPABASE_CONFIG = {
  url: 'https://wvhyedtgiwknjjayubbt.supabase.co', // https://xxxxx.supabase.co
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind2aHllZHRnaXdrbmpqYXl1YmJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMDc4NzIsImV4cCI6MjA2MzU4Mzg3Mn0.eJuPIpH0Xt2UQXtMIjlAXp1kYrlBsbRaSsgV-6PO7MM', // eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  serviceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind2aHllZHRnaXdrbmpqYXl1YmJ0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAwNzg3MiwiZXhwIjoyMDYzNTgzODcyfQ.C6fpMm_qXG_BZfZpyb3G8Kycj1WvXXGFrpa_OqjrIwE', // Service role key for admin operations
};

// App Configuration
export const APP_CONFIG = {
  name: 'DITIB Cenaze Takip',
  version: '1.0.0',
  environment: __DEV__ ? 'development' : 'production',
  debug: {
    enableVerboseLogging: true,
    showSessionTimeoutLogs: true,
  },
  // Support yapılandırması
  support: {
    email: '<EMAIL>',
    website: 'https://zsu-ev.eu/',
  },
  // UI yapılandırması
  ui: {
    placeholders: {
      email: '<EMAIL>',
    },
  },
  // Email yapılandırması
  emails: {
    // Varsayılan email adresleri
    default: {
      family: '<EMAIL>',
      support: '<EMAIL>',
      admin: '<EMAIL>',
    },
    // Departman email adresleri
    departments: {
      driver: '<EMAIL>',
      mosque: {
        fatih: '<EMAIL>',
        merkez: '<EMAIL>',
      },
      cemetery: {
        karacaahmet: '<EMAIL>',
        merkez: '<EMAIL>',
      },
    },
    // İletişim kişileri
    contacts: [
      {
        id: '1',
        name: 'Sorumlu Sürücü',
        role: 'Sorumlu Sürücü',
        phone: '+90 532 123 45 67',
        email: '<EMAIL>',
        department: 'driver',
      },
      {
        id: '2',
        name: 'DITIB Destek',
        role: '7/24 Destek Hattı',
        phone: '+90 212 XXX XX XX',
        email: '<EMAIL>',
        department: 'support',
      },
      {
        id: '3',
        name: 'Fatih Camii',
        role: 'Cenaze Töreni',
        phone: '+90 212 YYY YY YY',
        email: '<EMAIL>',
        department: 'mosque',
      },
      {
        id: '4',
        name: 'Karacaahmet Mezarlığı',
        role: 'Defin İşlemleri',
        phone: '+90 216 ZZZ ZZ ZZ',
        email: '<EMAIL>',
        department: 'cemetery',
      },
    ],
  },
  // Session timeout yapılandırması
  session: {
    timeoutMinutes: 10, // 10 dakika sonra otomatik çıkış
    warningMinutes: 5,  // 5 dakika kala uyarı göster
    checkIntervalSeconds: 60, // Her 30 saniyede bir kontrol et
  },
};

// API Endpoints
export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    magicLink: '/auth/magic-link',
  },
  cases: {
    list: '/cases',
    detail: '/cases/:id',
    create: '/cases',
    update: '/cases/:id',
    delete: '/cases/:id',
  },
  documents: {
    upload: '/documents/upload',
    download: '/documents/:id',
    delete: '/documents/:id',
  },
};

// Validation Rules
export const VALIDATION_RULES = {
  email: {
    required: 'E-posta adresi gereklidir',
    pattern: {
      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
      message: 'Geçersiz e-posta adresi',
    },
  },
  password: {
    required: 'Şifre gereklidir',
    minLength: {
      value: 8,
      message: 'Şifre en az 8 karakter olmalıdır',
    },
  },
};
