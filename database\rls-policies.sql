-- DITIB Funeral App - Row Level Security Policies
-- Bu dosyayı Supabase SQL Editor'de çalıştırarak RLS politikalarını ekleyin

-- 1. Mevcut RLS durumunu kontrol et
SELECT
  'Current RLS Status:' as info,
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
  AND tablename IN ('users', 'deceased', 'cases', 'tasks', 'documents', 'notifications')
ORDER BY tablename;

-- 2. Mevcut politikaları listele
SELECT
  'Existing Policies:' as info,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. Eski politikaları temizle (eğer varsa)
DROP POLICY IF EXISTS "Users can read their own data" ON public.users;
DROP POLICY IF EXISTS "Service role can manage users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can read users" ON public.users;
DROP POLICY IF EXISTS "Admin users can manage users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can read all users" ON public.users;
DROP POLICY IF EXISTS "Service role full access to users" ON public.users;
DROP POLICY IF EXISTS "Anonymous users can check emails" ON public.users;

DROP POLICY IF EXISTS "Users can read deceased" ON public.deceased;
DROP POLICY IF EXISTS "Service role can manage deceased" ON public.deceased;
DROP POLICY IF EXISTS "Authenticated users can read deceased" ON public.deceased;
DROP POLICY IF EXISTS "Service role full access to deceased" ON public.deceased;

DROP POLICY IF EXISTS "Users can read cases" ON public.cases;
DROP POLICY IF EXISTS "Service role can manage cases" ON public.cases;
DROP POLICY IF EXISTS "Authenticated users can read cases" ON public.cases;
DROP POLICY IF EXISTS "Admin users can manage cases" ON public.cases;
DROP POLICY IF EXISTS "Service role full access to cases" ON public.cases;

DROP POLICY IF EXISTS "Users can read tasks" ON public.tasks;
DROP POLICY IF EXISTS "Service role can manage tasks" ON public.tasks;
DROP POLICY IF EXISTS "Authenticated users can read tasks" ON public.tasks;
DROP POLICY IF EXISTS "Admin and Driver users can manage tasks" ON public.tasks;
DROP POLICY IF EXISTS "Service role full access to tasks" ON public.tasks;

DROP POLICY IF EXISTS "Authenticated users can read task_status_history" ON public.task_status_history;
DROP POLICY IF EXISTS "Admin and Driver users can manage task_status_history" ON public.task_status_history;
DROP POLICY IF EXISTS "Service role full access to task_status_history" ON public.task_status_history;

DROP POLICY IF EXISTS "Users can read documents" ON public.documents;
DROP POLICY IF EXISTS "Service role can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Authenticated users can read documents" ON public.documents;
DROP POLICY IF EXISTS "Admin and Driver users can manage documents" ON public.documents;
DROP POLICY IF EXISTS "Service role full access to documents" ON public.documents;

DROP POLICY IF EXISTS "Users can read notifications" ON public.notifications;
DROP POLICY IF EXISTS "Service role can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Admin users can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Service role full access to notifications" ON public.notifications;

DROP POLICY IF EXISTS "Authenticated users can read lookup_values" ON public.lookup_values;
DROP POLICY IF EXISTS "Admin users can manage lookup_values" ON public.lookup_values;
DROP POLICY IF EXISTS "Service role full access to lookup_values" ON public.lookup_values;

-- 4. USERS TABLE RLS Policies
-- Authenticated kullanıcılar tüm kullanıcıları okuyabilir (basit yaklaşım)
CREATE POLICY "Authenticated users can read all users" ON public.users
  FOR SELECT
  TO authenticated
  USING (true);

-- Admin kullanıcılar user'ları güncelleyebilir (basitleştirilmiş - infinite recursion'ı önlemek için)
CREATE POLICY "Admin users can update users" ON public.users
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to users" ON public.users
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Anon kullanıcılar sadece email kontrolü yapabilir (hesap oluşturma için)
CREATE POLICY "Anonymous users can check emails" ON public.users
  FOR SELECT
  TO anon
  USING (true);

-- 5. DECEASED TABLE RLS Policies
-- Authenticated kullanıcılar tüm deceased kayıtlarını okuyabilir
CREATE POLICY "Authenticated users can read deceased" ON public.deceased
  FOR SELECT
  TO authenticated
  USING (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to deceased" ON public.deceased
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 6. CASES TABLE RLS Policies
-- Authenticated kullanıcılar tüm cases'leri okuyabilir
CREATE POLICY "Authenticated users can read cases" ON public.cases
  FOR SELECT
  TO authenticated
  USING (true);

-- ADMIN kullanıcılar cases'leri yönetebilir (basitleştirilmiş)
CREATE POLICY "Admin users can manage cases" ON public.cases
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to cases" ON public.cases
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 7. TASKS TABLE RLS Policies (basitleştirilmiş)
-- Authenticated kullanıcılar tüm tasks'leri okuyabilir
CREATE POLICY "Authenticated users can read tasks" ON public.tasks
  FOR SELECT
  TO authenticated
  USING (true);

-- Authenticated kullanıcılar tasks'leri yönetebilir
CREATE POLICY "Authenticated users can manage tasks" ON public.tasks
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to tasks" ON public.tasks
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 7.5. TASK_STATUS_HISTORY TABLE RLS Policies (basitleştirilmiş)
-- Authenticated kullanıcılar task history'sini okuyabilir
CREATE POLICY "Authenticated users can read task history" ON public.task_status_history
  FOR SELECT
  TO authenticated
  USING (true);

-- Authenticated kullanıcılar task history oluşturabilir
CREATE POLICY "Authenticated users can create task history" ON public.task_status_history
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to task_status_history" ON public.task_status_history
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 8. DOCUMENTS TABLE RLS Policies
-- Authenticated kullanıcılar tüm documents'leri okuyabilir
CREATE POLICY "Authenticated users can read documents" ON public.documents
  FOR SELECT
  TO authenticated
  USING (true);

-- Authenticated kullanıcılar documents'leri yönetebilir (basitleştirilmiş)
CREATE POLICY "Authenticated users can manage documents" ON public.documents
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to documents" ON public.documents
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 9. NOTIFICATIONS TABLE RLS Policies
-- Authenticated kullanıcılar tüm notifications'ları okuyabilir
CREATE POLICY "Authenticated users can read notifications" ON public.notifications
  FOR SELECT
  TO authenticated
  USING (true);

-- Authenticated kullanıcılar notifications'ları yönetebilir (basitleştirilmiş)
CREATE POLICY "Authenticated users can manage notifications" ON public.notifications
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to notifications" ON public.notifications
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 10. LOOKUP_VALUES TABLE RLS Policies
-- Authenticated kullanıcılar tüm lookup values'ları okuyabilir
CREATE POLICY "Authenticated users can read lookup_values" ON public.lookup_values
  FOR SELECT
  TO authenticated
  USING (true);

-- Authenticated kullanıcılar lookup values'ları yönetebilir (basitleştirilmiş)
CREATE POLICY "Authenticated users can manage lookup_values" ON public.lookup_values
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Service role tüm işlemleri yapabilir
CREATE POLICY "Service role full access to lookup_values" ON public.lookup_values
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 11. RLS'nin aktif olduğundan emin ol
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deceased ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lookup_values ENABLE ROW LEVEL SECURITY;

-- 11. Sonuçları kontrol et
SELECT
  'RLS Policies Created Successfully!' as result,
  COUNT(*) as total_policies
FROM pg_policies
WHERE schemaname = 'public';

-- 12. Politikaları listele
SELECT
  'Created Policies:' as info,
  tablename,
  policyname,
  cmd as operation,
  roles
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 13. Test sorgusu - Users tablosunu kontrol et
SELECT
  'Users Table Test:' as test,
  COUNT(*) as user_count,
  COUNT(*) FILTER (WHERE email = '<EMAIL>') as qubbeba_count
FROM public.users;

-- 14. Başarı mesajı
SELECT '✅ RLS Policies configured successfully!' as message;
SELECT 'All tables now have appropriate Row Level Security policies.' as info;
SELECT 'Anonymous users can check emails for account creation.' as note1;
SELECT 'Authenticated users can read data based on their roles.' as note2;
SELECT 'Service role has full access for backend operations.' as note3;
