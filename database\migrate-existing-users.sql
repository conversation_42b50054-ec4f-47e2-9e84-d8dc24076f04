-- DITIB Funeral App - Migrate Existing Auth Users
-- Bu script mevcut auth.users'daki kull<PERSON>ıları yeni users tablosuna ekler

-- 1. Mevcut auth.users'ları kontrol et
DO $$
BEGIN
  RAISE NOTICE 'Checking existing auth.users...';
END $$;

-- 2. Auth.users'dan users tablosuna migration
-- Not: Bu işlem sadece admin yetkisiyle yapılabilir
INSERT INTO public.users (id, email, full_name, role, status, created_at)
SELECT 
  au.id,
  au.email,
  COALESCE(
    au.raw_user_meta_data->>'full_name',
    au.raw_user_meta_data->>'name', 
    split_part(au.email, '@', 1)
  ) as full_name,
  COALESCE(
    au.raw_user_meta_data->>'role',
    'ADMIN'
  )::TEXT as role,
  'ACTIVE' as status,
  au.created_at
FROM auth.users au
WHERE au.email IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM public.users u WHERE u.email = au.email
  )
ON CONFLICT (email) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  role = EXCLUDED.role,
  status = 'ACTIVE';

-- 3. Sonuçları göster
SELECT 
  'Migration Results' as info,
  COUNT(*) as migrated_users
FROM public.users;

-- 4. Migrated kullanıcıları listele
SELECT 
  'Migrated Users:' as info,
  email,
  full_name,
  role,
  status
FROM public.users
ORDER BY created_at;

-- 5. Özel durumlar için manuel ekleme
-- <EMAIL> için özel kayıt (eğer auth.users'da varsa ama metadata eksikse)
INSERT INTO public.users (email, full_name, role, status, created_at)
VALUES ('<EMAIL>', 'Qubbeba User', 'ADMIN', 'ACTIVE', NOW())
ON CONFLICT (email) DO UPDATE SET
  full_name = COALESCE(users.full_name, EXCLUDED.full_name),
  role = COALESCE(users.role, EXCLUDED.role),
  status = 'ACTIVE';

-- 6. Başarı mesajı
SELECT '✅ User migration completed!' as result;
SELECT 'All existing auth.users have been migrated to the new users table.' as message;

-- 7. Verification query
SELECT 
  'Final User Count:' as info,
  COUNT(*) as total_users,
  COUNT(*) FILTER (WHERE role = 'ADMIN') as admin_users,
  COUNT(*) FILTER (WHERE role = 'DRIVER') as driver_users,
  COUNT(*) FILTER (WHERE role = 'FAMILY') as family_users
FROM public.users;
