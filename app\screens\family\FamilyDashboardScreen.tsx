import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from 'react-i18next';

interface FamilyDashboardScreenProps {
  user: User;
  onNavigate: (screen: string) => void;
}

const FamilyDashboardScreen = ({ user, onNavigate }: FamilyDashboardScreenProps) => {
  const { t } = useTranslation();
  // Mock data - gerçek uygulamada API'den gelecek
  const currentCase = {
    id: '1',
    deceased_name: '<PERSON><PERSON>',
    status: 'IN_PROGRESS',
    progress: 65,
    created_at: '2024-01-20',
    estimated_completion: '2024-01-22',
    assigned_driver: '<PERSON>',
    driver_phone: '+90 532 123 45 67',
    next_step: 'Belge teslimi',
    location: 'Fatih Camii',
  };

  const processSteps = [
    {
      id: 1,
      title: '<PERSON><PERSON><PERSON><PERSON>ı',
      description: '<PERSON><PERSON><PERSON> sü<PERSON>ci başlatıldı',
      status: 'COMPLETED',
      date: '20.01.2024 09:00',
    },
    {
      id: 2,
      title: 'Sürücü Atandı',
      description: 'Ali Kaya görevlendirildi',
      status: 'COMPLETED',
      date: '20.01.2024 09:30',
    },
    {
      id: 3,
      title: 'Hastane İşlemleri',
      description: 'Gerekli belgeler alındı',
      status: 'COMPLETED',
      date: '20.01.2024 11:00',
    },
    {
      id: 4,
      title: 'Belge Teslimi',
      description: 'Nüfus müdürlüğüne teslim',
      status: 'IN_PROGRESS',
      date: 'Devam ediyor',
    },
    {
      id: 5,
      title: 'Cenaze Töreni',
      description: 'Fatih Camii\'nde tören',
      status: 'PENDING',
      date: 'Bekliyor',
    },
    {
      id: 6,
      title: 'Defin İşlemi',
      description: 'Karacaahmet Mezarlığı',
      status: 'PENDING',
      date: 'Bekliyor',
    },
  ];

  const recentUpdates = [
    {
      id: 1,
      title: 'Belge Onaylandı',
      description: 'Ölüm belgesi nüfus müdürlüğü tarafından onaylandı',
      time: '2 saat önce',
      icon: 'checkmark-circle',
      color: '#2ECC71',
    },
    {
      id: 2,
      title: 'Sürücü Yola Çıktı',
      description: 'Ali Kaya belge teslimi için yola çıktı',
      time: '3 saat önce',
      icon: 'car',
      color: '#F39C12',
    },
    {
      id: 3,
      title: 'Randevu Planlandı',
      description: 'Cenaze töreni için cami ile görüşüldü',
      time: '5 saat önce',
      icon: 'calendar',
      color: '#9B59B6',
    },
  ];

  const quickActions = [
    {
      title: 'Süreç Takibi',
      description: 'Detaylı süreç durumu',
      icon: 'eye',
      color: '#0D3B66',
      onPress: () => onNavigate('tracking'),
    },
    {
      title: 'Belgeler',
      description: 'İlgili evrakları görün',
      icon: 'document-text',
      color: '#9B59B6',
      onPress: () => onNavigate('documents'),
    },
    {
      title: 'İletişim',
      description: 'Personelle konuşun',
      icon: 'chatbubbles',
      color: '#2ECC71',
      onPress: () => onNavigate('communication'),
    },
    {
      title: 'Bildirimler',
      description: 'Güncellemeleri takip edin',
      icon: 'notifications',
      color: '#FAA916',
      onPress: () => onNavigate('notifications'),
    },
  ];

  const handleCallDriver = () => {
    Alert.alert(
      'Sürücü İletişimi',
      `${currentCase.assigned_driver} ile iletişime geçmek istediğinizden emin misiniz?\n\n${currentCase.driver_phone}`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Ara', onPress: () => console.log(`Calling ${currentCase.driver_phone}`) },
      ]
    );
  };

  const handleEmergencyCall = () => {
    Alert.alert(
      'Acil Durum',
      'DITIB 7/24 destek hattını aramak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: t('common.call'), onPress: () => console.log('Emergency call initiated') },
      ]
    );
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'checkmark-circle';
      case 'IN_PROGRESS': return 'time';
      case 'PENDING': return 'ellipse-outline';
      default: return 'ellipse-outline';
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return '#2ECC71';
      case 'IN_PROGRESS': return '#F39C12';
      case 'PENDING': return '#E1E8ED';
      default: return '#E1E8ED';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <View style={styles.welcomeHeader}>
            <View style={styles.logoContainer}>
              <Ionicons name="people" size={32} color="#FFFFFF" />
            </View>
            <View style={styles.welcomeContent}>
              <Text style={styles.roleTitle}>{user.role === 'FAMILY' ? 'Aile' : user.role}</Text>
              <Text style={styles.userName}>{user.full_name}</Text>
            </View>
          </View>
          <Text style={styles.welcomeSubtitle}>
            Size en iyi hizmeti sunmak için buradayız
          </Text>
        </View>

        {/* Emergency Contact */}
        <View style={styles.emergencySection}>
          <TouchableOpacity style={styles.emergencyButton} onPress={handleEmergencyCall}>
            <Ionicons name="call" size={20} color="#FFFFFF" />
            <Text style={styles.emergencyText}>7/24 DESTEK HATTI</Text>
          </TouchableOpacity>
        </View>

        {/* Current Case Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mevcut Vaka Durumu</Text>
          <View style={styles.caseCard}>
            <View style={styles.caseHeader}>
              <View style={styles.caseInfo}>
                <Text style={styles.deceasedName}>{currentCase.deceased_name}</Text>
                <Text style={styles.caseSubtitle}>Cenaze Süreci</Text>
                <View style={styles.statusBadge}>
                  <Text style={styles.statusText}>DEVAM EDİYOR</Text>
                </View>
              </View>
              <View style={styles.progressCircle}>
                <Text style={styles.progressText}>{currentCase.progress}%</Text>
              </View>
            </View>

            <View style={styles.progressSection}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${currentCase.progress}%` }]} />
              </View>
              <Text style={styles.progressLabel}>
                {currentCase.progress}% Tamamlandı
              </Text>
            </View>

            <View style={styles.caseDetails}>
              <View style={styles.detailRow}>
                <Ionicons name="person" size={16} color="#8E9297" />
                <Text style={styles.detailText}>Sorumlu: {currentCase.assigned_driver}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="location" size={16} color="#8E9297" />
                <Text style={styles.detailText}>Konum: {currentCase.location}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="arrow-forward-circle" size={16} color="#9B59B6" />
                <Text style={styles.detailText}>Sonraki: {currentCase.next_step}</Text>
              </View>
            </View>

            <TouchableOpacity style={styles.callDriverButton} onPress={handleCallDriver}>
              <Ionicons name="call" size={16} color="#9B59B6" />
              <Text style={styles.callDriverText}>Sorumlu Kişiyi Ara</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Process Timeline */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Süreç Durumu</Text>
            <TouchableOpacity onPress={() => onNavigate('tracking')}>
              <Text style={styles.seeAllText}>Detay</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.timelineContainer}>
            {processSteps.slice(0, 4).map((step, index) => (
              <View key={step.id} style={styles.timelineItem}>
                <View style={styles.timelineIconContainer}>
                  <View style={[
                    styles.timelineIcon,
                    { backgroundColor: getStepColor(step.status) }
                  ]}>
                    <Ionicons
                      name={getStepIcon(step.status) as any}
                      size={16}
                      color={step.status === 'PENDING' ? '#8E9297' : '#FFFFFF'}
                    />
                  </View>
                  {index < 3 && (
                    <View style={[
                      styles.timelineLine,
                      { backgroundColor: step.status === 'COMPLETED' ? '#2ECC71' : '#E1E8ED' }
                    ]} />
                  )}
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineTitle}>{step.title}</Text>
                  <Text style={styles.timelineDescription}>{step.description}</Text>
                  <Text style={styles.timelineDate}>{step.date}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Recent Updates */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Son Güncellemeler</Text>
          {recentUpdates.map((update) => (
            <View key={update.id} style={styles.updateCard}>
              <View style={[styles.updateIcon, { backgroundColor: update.color }]}>
                <Ionicons name={update.icon as any} size={16} color="#FFFFFF" />
              </View>
              <View style={styles.updateContent}>
                <Text style={styles.updateTitle}>{update.title}</Text>
                <Text style={styles.updateDescription}>{update.description}</Text>
                <Text style={styles.updateTime}>{update.time}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hızlı Erişim</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionCard}
                onPress={action.onPress}
              >
                <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
                  <Ionicons name={action.icon as any} size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.quickActionTitle}>{action.title}</Text>
                <Text style={styles.quickActionDescription}>{action.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Support Info */}
        <View style={styles.section}>
          <View style={styles.supportCard}>
            <View style={styles.supportHeader}>
              <Ionicons name="headset" size={24} color="#9B59B6" />
              <Text style={styles.supportTitle}>7/24 Destek Hizmeti</Text>
            </View>
            <Text style={styles.supportPhone}>0212 XXX XX XX</Text>
            <Text style={styles.supportDescription}>
              Herhangi bir sorunuz olduğunda bizi arayabilirsiniz.
              Size en iyi hizmeti sunmak için buradayız.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  welcomeSection: {
    backgroundColor: '#9B59B6',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  welcomeContent: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#E1C4F0',
    marginBottom: 4,
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#E1C4F0',
  },
  emergencySection: {
    paddingHorizontal: 24,
    marginTop: -16,
  },
  emergencyButton: {
    backgroundColor: '#E74C3C',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emergencyText: {
    color: '#FFFFFF',
    fontWeight: '700',
    marginLeft: 8,
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 24,
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  seeAllText: {
    fontSize: 14,
    color: '#9B59B6',
    fontWeight: '500',
  },
  caseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  caseInfo: {
    flex: 1,
  },
  deceasedName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  caseSubtitle: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  statusBadge: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1B5E20',
  },
  progressCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#9B59B6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E1E8ED',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#9B59B6',
    borderRadius: 4,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#9B59B6',
    textAlign: 'center',
  },
  caseDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#8E9297',
    marginLeft: 8,
  },
  callDriverButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  callDriverText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#9B59B6',
    marginLeft: 8,
  },
  timelineContainer: {
    marginTop: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineIconContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineLine: {
    width: 2,
    height: 24,
    marginTop: 4,
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  timelineDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 4,
  },
  timelineDate: {
    fontSize: 12,
    color: '#B8C5D1',
  },
  updateCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  updateIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  updateContent: {
    flex: 1,
  },
  updateTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  updateDescription: {
    fontSize: 13,
    color: '#8E9297',
    marginBottom: 4,
  },
  updateTime: {
    fontSize: 12,
    color: '#B8C5D1',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  quickActionCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
    textAlign: 'center',
  },
  quickActionDescription: {
    fontSize: 12,
    color: '#8E9297',
    textAlign: 'center',
  },
  supportCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginTop: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  supportHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  supportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginLeft: 12,
  },
  supportPhone: {
    fontSize: 20,
    fontWeight: '700',
    color: '#9B59B6',
    marginBottom: 8,
  },
  supportDescription: {
    fontSize: 14,
    color: '#8E9297',
    lineHeight: 20,
  },
});

export default FamilyDashboardScreen;
