-- Migration Script: Tasks Table Optimization
-- Adds location-based progress tracking and removes mock data dependency
-- Version: 1.0
-- Date: 2024-01-20

-- Add new columns to existing tasks table
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS title VARCHAR(255),
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'MEDIUM' 
  CHECK (priority IN ('URGENT', 'HIGH', 'MEDIUM', 'LOW')),
ADD COLUMN IF NOT EXISTS due_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS location VARCHAR(500),
ADD COLUMN IF NOT EXISTS target_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS target_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 
  CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update status enum to match new requirements
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_status_check;
ALTER TABLE public.tasks ADD CONSTRAINT tasks_status_check 
CHECK (status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'OVERDUE', 'CANCELLED'));

-- Update task_type enum to include new types
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_task_type_check;
ALTER TABLE public.tasks ADD CONSTRAINT tasks_task_type_check 
CHECK (task_type IN (
  'PICK_UP_FROM_MORGUE', 'TO_AIRPORT', 'TO_CONSULATE', 'DELIVERED',
  'DOCUMENT_DELIVERY', 'FAMILY_MEETING', 'HOSPITAL_TRANSFER'
));

-- Update sub_status enum
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_sub_status_check;
ALTER TABLE public.tasks ADD CONSTRAINT tasks_sub_status_check 
CHECK (sub_status IN (
  'PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'ON_HOLD', 'WAITING_APPROVAL'
));

-- Migrate existing data
UPDATE public.tasks SET 
  title = CASE task_type
    WHEN 'PICK_UP_FROM_MORGUE' THEN 'Morgue''dan Alma'
    WHEN 'TO_AIRPORT' THEN 'Havaalanına Nakil'
    WHEN 'TO_CONSULATE' THEN 'Konsolosluğa Nakil'
    WHEN 'DELIVERED' THEN 'Teslim Edildi'
    ELSE 'Görev'
  END,
  description = CASE task_type
    WHEN 'PICK_UP_FROM_MORGUE' THEN 'Hastane morgue''undan cenaze alma işlemi'
    WHEN 'TO_AIRPORT' THEN 'Havaalanına cenaze nakil işlemi'
    WHEN 'TO_CONSULATE' THEN 'Konsolosluğa evrak teslim işlemi'
    WHEN 'DELIVERED' THEN 'Cenaze teslim işlemi'
    ELSE 'Görev açıklaması'
  END,
  priority = 'HIGH',
  progress_percentage = CASE status
    WHEN 'DONE' THEN 100
    WHEN 'ACTIVE' THEN 50
    WHEN 'FAILED' THEN 0
    WHEN 'OVERDUE' THEN 0
    ELSE 0
  END,
  due_at = scheduled_at + INTERVAL '4 hours',
  estimated_duration_minutes = 120
WHERE title IS NULL;

-- Update status mapping from old to new
UPDATE public.tasks SET status = 'IN_PROGRESS' WHERE status = 'ACTIVE';
UPDATE public.tasks SET status = 'COMPLETED' WHERE status = 'DONE';

-- Make title column NOT NULL after data migration
ALTER TABLE public.tasks ALTER COLUMN title SET NOT NULL;

-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at 
BEFORE UPDATE ON public.tasks
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create enhanced indexes for performance
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_status 
ON public.tasks(assignee_id, status);

CREATE INDEX IF NOT EXISTS idx_tasks_priority_due_at 
ON public.tasks(priority DESC, due_at ASC);

CREATE INDEX IF NOT EXISTS idx_tasks_progress 
ON public.tasks(progress_percentage);

CREATE INDEX IF NOT EXISTS idx_tasks_location 
ON public.tasks(target_latitude, target_longitude);

CREATE INDEX IF NOT EXISTS idx_tasks_updated_at 
ON public.tasks(updated_at);

-- Insert sample data with location coordinates
INSERT INTO public.tasks (
  case_id, assignee_id, task_type, title, description,
  status, priority, scheduled_at, due_at, 
  estimated_duration_minutes, location, 
  target_latitude, target_longitude, progress_percentage
) 
SELECT 
  c.id as case_id,
  u.id as assignee_id,
  'PICK_UP_FROM_MORGUE' as task_type,
  'Morgue''dan Alma' as title,
  'Hastane morgue''undan cenaze alma işlemi' as description,
  'PENDING' as status,
  'HIGH' as priority,
  NOW() + INTERVAL '2 hours' as scheduled_at,
  NOW() + INTERVAL '6 hours' as due_at,
  90 as estimated_duration_minutes,
  'Şişli Etfal Hastanesi Morgue' as location,
  41.0602 as target_latitude, -- Şişli Etfal Hastanesi
  28.9942 as target_longitude,
  0 as progress_percentage
FROM public.cases c
CROSS JOIN public.users u
WHERE u.role = 'DRIVER'
AND NOT EXISTS (
  SELECT 1 FROM public.tasks t 
  WHERE t.case_id = c.id AND t.task_type = 'PICK_UP_FROM_MORGUE'
)
LIMIT 1;

-- Success message
SELECT '✅ Tasks table optimization migration completed successfully!' as result;
SELECT '📍 Location-based progress tracking enabled' as feature;
SELECT '🚫 Mock data dependency removed' as improvement;